-- Database initialization script for diagnosis system
-- This script sets up the required extensions and tables

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create diagnoses table if it doesn't exist
CREATE TABLE IF NOT EXISTS diagnoses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    symptoms TEXT,
    treatment TEXT,
    notes TEXT,
    embedding vector(1536),  -- OpenAI embedding dimension
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on embedding column for faster similarity search
CREATE INDEX IF NOT EXISTS diagnoses_embedding_idx ON diagnoses 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create index on name for faster lookups
CREATE INDEX IF NOT EXISTS diagnoses_name_idx ON diagnoses (name);

-- Create sessions table for API session management (optional)
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symptoms_history TEXT[],
    previous_questions TEXT[],
    excluded_diagnoses TEXT[],
    question_count INTEGER DEFAULT 0,
    max_questions INTEGER DEFAULT 5,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on last_activity for session cleanup
CREATE INDEX IF NOT EXISTS chat_sessions_last_activity_idx ON chat_sessions (last_activity);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_diagnoses_updated_at 
    BEFORE UPDATE ON diagnoses 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data if table is empty (optional)
INSERT INTO diagnoses (name, description, symptoms, treatment, notes)
SELECT 
    'Sample Diagnosis',
    'This is a sample diagnosis for testing purposes',
    'Sample symptoms for testing',
    'Sample treatment for testing',
    'Sample notes for testing'
WHERE NOT EXISTS (SELECT 1 FROM diagnoses LIMIT 1);

-- Grant permissions (adjust as needed)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
