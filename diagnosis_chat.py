#!/usr/bin/env python3
"""
Terminal-based Diagnosis Chat System
Acts as a medical diagnosis expert using vector search with Ollama
Asks follow-up questions (max 5) and provides diagnosis with confidence scores
"""

import os
import sys
import psycopg2
import requests
import logging
import openai
from typing import List, Dict, <PERSON><PERSON>
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce log noise for chat interface
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OpenAIEmbedder:
    """Embedder using OpenAI API for text embeddings"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

        logger.info(f"Initialized OpenAIEmbedder with model: {self.model_name}")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using OpenAI API"""
        embeddings = []

        try:
            # OpenAI API supports batch processing
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])

                for item in data:
                    embedding = item.get("embedding", [])
                    if embedding:
                        embeddings.append(embedding)
                    else:
                        logger.warning(f"Empty embedding in response")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]  # OpenAI default dimension
                        embeddings.append(random_embedding)

                logger.info(f"Generated {len(embeddings)} embeddings successfully")
            else:
                logger.error(f"Error from OpenAI API: {response.status_code} - {response.text}")
                # Generate random embeddings as fallback
                for _ in texts:
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                    embeddings.append(random_embedding)

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            # Generate random embeddings as fallback
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                embeddings.append(random_embedding)

        return embeddings

class DiagnosisDatabase:
    """Database manager for diagnosis vector search"""

    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        
        if self.database_url:
            self.db_config = None
        else:
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }

    def get_connection(self):
        """Get database connection"""
        if self.database_url:
            return psycopg2.connect(self.database_url)
        else:
            return psycopg2.connect(**self.db_config)

    def search_similar_diagnoses(self, query_embedding: List[float], top_k: int = 5) -> List[Dict]:
        """Search for similar diagnoses using vector similarity"""
        
        search_sql = """
        SELECT
            name,
            description,
            symptoms,
            treatment,
            notes,
            CASE
                WHEN embedding IS NULL THEN 0.0
                WHEN %s::vector IS NULL THEN 0.0
                ELSE GREATEST(0.0, LEAST(1.0, 1 - (embedding <=> %s::vector)))
            END as confidence_score
        FROM diagnoses
        WHERE embedding IS NOT NULL
        ORDER BY embedding <=> %s::vector
        LIMIT %s
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    print("Running SQL query:")
                    print(cur.mogrify(search_sql, (query_embedding, query_embedding, query_embedding, top_k)).decode())
                    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
                    
                    results = cur.fetchall()

                    diagnoses = []
                    for row in results:
                        diagnoses.append({
                            "name": row[0],
                            "description": row[1],
                            "symptoms": row[2],
                            "treatment": row[3],
                            "notes": row[4],
                            "confidence_score": float(row[5])
                        })

                    return diagnoses

        except Exception as e:
            logger.error(f"Error searching diagnoses: {e}")
            return []

    def get_diagnosis_count(self) -> int:
        """Get total number of diagnoses in database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM diagnoses")
                    return cur.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting diagnosis count: {e}")
            return 0

class GroqLLM:
    """LLM interface using Groq API"""

    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "llama-3.3-70b-versatile"

        if not self.api_key:
            logger.warning("GROQ_API_KEY not found in environment variables")

    def _extract_symptoms_summary(self, conversation_history: List[Dict]) -> str:
        """Extract a clean summary of current symptoms from conversation history"""
        if not conversation_history:
            return "No symptoms provided"

        # Get initial symptoms
        initial_symptoms = ""
        additional_info = []

        for step in conversation_history:
            if step.get('type') == 'initial_symptoms':
                initial_symptoms = step.get('content', '')
            elif step.get('type') == 'answer':
                # Only include relevant symptom information from answers
                answer = step.get('content', '').strip()
                if answer and not answer.lower().startswith('no'):
                    additional_info.append(answer)

        # Combine initial symptoms with relevant additional information
        if additional_info:
            return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
        else:
            return initial_symptoms

    def generate_follow_up_question(self, symptoms_so_far: str, top_diagnoses: List[Dict], previous_questions: List[str], conversation_history: List[Dict] = None) -> str:
        """Generate a follow-up question to narrow down diagnosis"""

        if not self.api_key:
            # Fallback questions if no API key
            fallback_questions = [
                "What other symptoms are you experiencing?",
                "How long have you been experiencing these symptoms?",
                "Are there any specific areas of pain or discomfort?",
                "Have you noticed any changes in appetite or behavior?",
                "Are there any environmental factors that might be relevant?"
            ]

            # Return a question that hasn't been asked yet
            for question in fallback_questions:
                if not any(q.lower() in question.lower() for q in previous_questions):
                    return question

            return None  # No more questions available

        # Create context from top diagnoses
        diagnosis_context = "\n".join([
            f"- {d['name']} (confidence: {d['confidence_score']:.2f}): {d['symptoms'][:200]}..."
            for d in top_diagnoses[:3]
        ])

        # Format conversation history as JSON
        import json
        conversation_json = "[]"
        if conversation_history:
            try:
                conversation_json = json.dumps(conversation_history, indent=2)
            except Exception:
                conversation_json = "[]"

        # Include excluded diagnoses context
        excluded_context = ""
        if hasattr(self, 'excluded_diagnoses') and self.excluded_diagnoses:
            excluded_context = f"\nExcluded diagnoses (don't ask about these): {', '.join(self.excluded_diagnoses)}"

        # Extract current symptoms summary from conversation history
        current_symptoms_summary = self._extract_symptoms_summary(conversation_history or [])

        prompt = f"""You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: {current_symptoms_summary}

Conversation history (JSON format):
{conversation_json}

Top potential diagnoses:
{diagnosis_context}{excluded_context}

IMPORTANT:
- Ask a specific, targeted question that would help differentiate between these diagnoses
- Do NOT repeat or rephrase any previous questions from the conversation history
- Do NOT ask about excluded diagnoses or their symptoms
- If the diagnoses are too similar or no good differentiating question exists, respond with "NO_QUESTION"
- Keep it concise and medical professional
- Only ask ONE question"""

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 150,
                    "temperature": 0.7
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                question = result["choices"][0]["message"]["content"].strip()

                # Check if AI indicates no good question exists
                if "NO_QUESTION" in question.upper():
                    return None

                return question
            else:
                return None

        except Exception as e:
            logger.error(f"Error generating follow-up question: {e}")
            return None

class OpenAILLM:
    """LLM interface using OpenAI API"""

    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-4o-mini-2024-07-18"  # You can change to another model if needed

        if not self.api_key:
            logger.warning("OPENAI_API_KEY not found in environment variables")
        openai.api_key = self.api_key

    def _extract_symptoms_summary(self, conversation_history: List[Dict]) -> str:
        """Extract a clean summary of current symptoms from conversation history"""
        if not conversation_history:
            return "No symptoms provided"

        # Get initial symptoms
        initial_symptoms = ""
        additional_info = []

        for step in conversation_history:
            if step.get('type') == 'initial_symptoms':
                initial_symptoms = step.get('content', '')
            elif step.get('type') == 'answer':
                # Only include relevant symptom information from answers
                answer = step.get('content', '').strip()
                if answer and not answer.lower().startswith('no'):
                    additional_info.append(answer)

        # Combine initial symptoms with relevant additional information
        if additional_info:
            return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
        else:
            return initial_symptoms

    def generate_follow_up_question(self, symptoms_so_far: str, top_diagnoses: List[Dict], previous_questions: List[str], conversation_history: List[Dict] = None) -> str:
        """Generate a follow-up question to narrow down diagnosis"""

        if not self.api_key:
            # Fallback questions if no API key
            fallback_questions = [
                "What other symptoms are you experiencing?",
                "How long have you been experiencing these symptoms?",
                "Are there any specific areas of pain or discomfort?",
                "Have you noticed any changes in appetite or behavior?",
                "Are there any environmental factors that might be relevant?"
            ]
            for question in fallback_questions:
                if not any(q.lower() in question.lower() for q in previous_questions):
                    return question
            return None

        diagnosis_context = "\n".join([
            f"- {d['name']} (confidence: {d['confidence_score']:.2f}): {d['symptoms'][:200]}..."
            for d in top_diagnoses[:3]
        ])

        # Format conversation history as JSON
        import json
        conversation_json = "[]"
        if conversation_history:
            try:
                conversation_json = json.dumps(conversation_history, indent=2)
            except Exception:
                conversation_json = "[]"

        excluded_context = ""
        if hasattr(self, 'excluded_diagnoses') and self.excluded_diagnoses:
            excluded_context = f"\nExcluded diagnoses (don't ask about these): {', '.join(self.excluded_diagnoses)}"

        # Extract current symptoms summary from conversation history
        current_symptoms_summary = self._extract_symptoms_summary(conversation_history or [])

        prompt = f"""You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: {current_symptoms_summary}

Conversation history (JSON format):
{conversation_json}

Top potential diagnoses:
{diagnosis_context}{excluded_context}

IMPORTANT:
- Ask a specific, targeted question that would help differentiate between these diagnoses
- Do NOT repeat or rephrase any previous questions from the conversation history
- Do NOT ask about excluded diagnoses or their symptoms
- If the diagnoses are too similar or no good differentiating question exists, respond with "NO_QUESTION"
- Keep it concise and medical professional
- Only ask ONE question"""
        print("prompt",prompt)
        try:
            response = openai.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.7
            )
            question = response.choices[0].message.content.strip()
            if "NO_QUESTION" in question.upper():
                return None
            return question
        except Exception as e:
            logger.error(f"Error generating follow-up question: {e}")
            return None

class DiagnosisChat:
    """Main diagnosis chat system"""

    def __init__(self):
        self.embedder = OpenAIEmbedder()
        self.database = DiagnosisDatabase()
        self.llm = OpenAILLM()  # <-- Use OpenAILLM here
        self.max_questions = 5
        self.question_count = 0
        self.symptoms_history = []
        self.previous_questions = []
        self.conversation_history = []  # Track question-answer pairs
        self.excluded_diagnoses = set()  # Track diagnoses to exclude based on user responses

    def start_chat(self):
        """Start the diagnosis chat session"""
        print("🏥 Medical Diagnosis Expert Chat")
        print("=" * 50)
        
        # Check database
        diagnosis_count = self.database.get_diagnosis_count()
        if diagnosis_count == 0:
            print("❌ No diagnoses found in database. Please run the embedding system first.")
            return

        print(f"📊 Database contains {diagnosis_count} diagnoses")
        print("\n🩺 I'm here to help with medical diagnosis based on your symptoms.")
        print("Please describe your symptoms, and I'll ask follow-up questions to narrow down the diagnosis.")
        print("Type 'quit' to exit.\n")

        # Get initial symptoms
        initial_symptoms = input("👤 Please describe your main symptoms: ").strip()
        
        if initial_symptoms.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            return
        self.symptoms_history.append(initial_symptoms)
        self.question_count = 0
        self.previous_questions = []
        self.conversation_history = [{"step": 1, "type": "initial_symptoms", "content": initial_symptoms}]
        self.excluded_diagnoses = set()

        # Start diagnosis process
        self.process_symptoms()

    def process_symptoms(self):
        """Process current symptoms and provide diagnosis or ask follow-up"""
        
        # Combine all symptoms
        combined_symptoms = " ".join(self.symptoms_history)
        print(f"Combined symptoms: {combined_symptoms}")

        # Get embedding for symptoms
        embeddings = self.embedder.get_embeddings([combined_symptoms])
        if not embeddings:
            print("❌ Error processing symptoms. Please try again.")
            return
        # Search for similar diagnoses
        diagnoses = self.database.search_similar_diagnoses(embeddings[0], top_k=10)
        # Filter out excluded diagnoses
        diagnoses = [d for d in diagnoses if d['name'] not in self.excluded_diagnoses]
        
        # Filter out diagnoses with confidence_score < 0.3 (30%)
        diagnoses = [d for d in diagnoses if d['confidence_score'] >= 0.4]
        
        if not diagnoses:
            print("❌ No matching diagnoses found. Please consult a medical professional.")
            return

        # Display current top diagnoses
        print(f"\n🔍 Current Analysis (Question {self.question_count + 1}):")
        print("-" * 40)
        
        for i, diagnosis in enumerate(diagnoses[:5], 1):
            confidence_percent = diagnosis['confidence_score'] * 100
            print(f"{i}. {diagnosis['name']}")
            print(f"   Confidence: {confidence_percent:.1f}%")
            print(f"   Key symptoms: {diagnosis['symptoms'][:100]}...")
            if diagnosis['treatment']:
                print(f"   Treatment: {diagnosis['treatment'][:80]}...")
            print()

        # Check if we should ask more questions
        top_confidence = diagnoses[0]['confidence_score']

        if self.question_count >= self.max_questions or top_confidence > 0.8:
            # Final diagnosis
            self.provide_final_diagnosis(diagnoses[0])
        else:
            # Try to ask follow-up question
            success = self.ask_follow_up_question(combined_symptoms, diagnoses)
            if not success:
                # No good question found, provide final diagnosis
                print("\n🎯 No further differentiating questions available.")
                self.provide_final_diagnosis(diagnoses[0])

    def ask_follow_up_question(self, symptoms_so_far: str, diagnoses: List[Dict]) -> bool:
        """Ask a follow-up question to narrow down diagnosis. Returns True if successful, False if no question available."""

        self.question_count += 1

        # Generate follow-up question
        question = self.llm.generate_follow_up_question(symptoms_so_far, diagnoses, self.previous_questions, self.conversation_history)

        if question is None:
            # No good question available
            return False

        print(f"🩺 Follow-up Question {self.question_count}/{self.max_questions}:")
        print(f"   {question}")

        # Store the question to avoid repetition
        self.previous_questions.append(question)

        # Add question to conversation history
        self.conversation_history.append({
            "step": len(self.conversation_history) + 1,
            "type": "question",
            "content": question,
            "question_number": self.question_count
        })

        # Get user response
        response = input("\n👤 Your answer: ").strip()

        if response.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            return True

        if response:
            self.symptoms_history.append(response)

            # Add answer to conversation history
            self.conversation_history.append({
                "step": len(self.conversation_history) + 1,
                "type": "answer",
                "content": response,
                "question_number": self.question_count
            })

            # Analyze response to exclude irrelevant diagnoses
            self.analyze_response_and_filter(response, diagnoses, question)

            self.process_symptoms()
            return True
        else:
            print("Please provide an answer to continue.")
            return self.ask_follow_up_question(symptoms_so_far, diagnoses)

    def analyze_response_and_filter(self, response: str, diagnoses: List[Dict], question: str):
        """Analyze user response and filter out irrelevant diagnoses"""

        response_lower = response.lower()
        question_lower = question.lower()

        # Simple keyword-based filtering
        negative_responses = ['no', 'not', 'none', 'never', 'absent', 'negative']
        positive_responses = ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']

        is_negative = any(neg in response_lower for neg in negative_responses)
        is_positive = any(pos in response_lower for pos in positive_responses)

        # If user gives a clear negative response, exclude relevant diagnoses
        if is_negative and not is_positive:

            # Special case: if question is about udder/milk and user says no, exclude all mastitis
            if any(term in question_lower for term in ['udder', 'milk', 'mammary']):
                # Exclude all mastitis-related diagnoses
                mastitis_patterns = ['mastitis', 'mammary', 'udder']

                # Add common mastitis diagnosis names to exclusion list
                mastitis_diagnoses = [
                    'Acute Mastitis', 'Sub Acute Mastitis', 'Mild Mastitis',
                    'Gangrenous Mastitis', 'Per Acute Mastitis', 'Chronic Mastitis',
                    'Clinical Mastitis', 'Subclinical Mastitis'
                ]

                for mastitis_name in mastitis_diagnoses:
                    self.excluded_diagnoses.add(mastitis_name)
                    if mastitis_name in [d['name'] for d in diagnoses[:5]]:
                        print(f"   ℹ️  Excluding {mastitis_name} based on your response")

                # Also check for any diagnosis containing mastitis keywords
                for diagnosis in diagnoses:
                    diagnosis_name_lower = diagnosis['name'].lower()
                    if any(keyword in diagnosis_name_lower for keyword in mastitis_patterns):
                        self.excluded_diagnoses.add(diagnosis['name'])
                        print(f"   ℹ️  Excluding {diagnosis['name']} based on your response")

            # General keyword-based exclusion
            else:
                question_keywords = self.extract_keywords_from_question(question)

                for diagnosis in diagnoses[:5]:  # Check top 5 diagnoses
                    diagnosis_symptoms = diagnosis['symptoms'].lower()
                    diagnosis_name = diagnosis['name'].lower()

                    # If the diagnosis heavily features the questioned symptom and user says no, exclude it
                    keyword_matches = sum(1 for keyword in question_keywords
                                        if keyword in diagnosis_symptoms or keyword in diagnosis_name)
                    if keyword_matches >= 1:  # If any keyword matches
                        self.excluded_diagnoses.add(diagnosis['name'])
                        print(f"   ℹ️  Excluding {diagnosis['name']} based on your response")

    def extract_keywords_from_question(self, question: str) -> List[str]:
        """Extract key medical terms from a question"""

        # Common medical keywords to look for
        medical_keywords = [
            'respiratory', 'breathing', 'cough', 'fever', 'temperature',
            'udder', 'milk', 'swollen', 'inflammation', 'pain', 'discharge',
            'appetite', 'eating', 'drinking', 'lethargy', 'weakness',
            'diarrhea', 'vomiting', 'skin', 'lesions', 'wounds',
            'lameness', 'walking', 'movement', 'joints', 'legs'
        ]

        question_lower = question.lower()
        found_keywords = []

        for keyword in medical_keywords:
            if keyword in question_lower:
                found_keywords.append(keyword)

        return found_keywords

    def provide_final_diagnosis(self, top_diagnosis: Dict):
        """Provide final diagnosis with recommendations"""
        
        confidence_percent = top_diagnosis['confidence_score'] * 100
        
        print("\n🎯 FINAL DIAGNOSIS:")
        print("=" * 50)
        print(f"Diagnosis: {top_diagnosis['name']}")
        print(f"Confidence: {confidence_percent:.1f}%")
        print(f"\nDescription: {top_diagnosis['description']}")
        print(f"\nSymptoms: {top_diagnosis['symptoms']}")
        
        if top_diagnosis['treatment']:
            print(f"\nRecommended Treatment: {top_diagnosis['treatment']}")
        
        if top_diagnosis['notes']:
            print(f"\nAdditional Notes: {top_diagnosis['notes']}")
        
        print("\n⚠️  IMPORTANT DISCLAIMER:")
        print("This is an AI-based analysis for informational purposes only.")
        print("Please consult with a qualified medical professional for proper diagnosis and treatment.")
        
        # Ask if user wants to start over
        print("\n" + "=" * 50)
        restart = input("Would you like to start a new diagnosis session? (y/n): ").strip().lower()
        
        if restart in ['y', 'yes']:
            self.symptoms_history = []
            self.question_count = 0
            self.previous_questions = []
            self.conversation_history = []
            self.excluded_diagnoses = set()
            print("\n" + "=" * 50)
            self.start_chat()
        else:
            print("👋 Thank you for using the Medical Diagnosis Expert Chat!")

def main():
    """Main entry point"""
    try:
        chat = DiagnosisChat()
        chat.start_chat()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("Please check your database connection and try again.")

if __name__ == "__main__":
    main()
