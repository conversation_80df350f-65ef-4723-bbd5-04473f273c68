#!/bin/bash
# Check status of diagnosis API containers and services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check service health
check_service_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    print_status "Checking $service_name..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        print_success "$service_name is healthy"
        return 0
    else
        print_error "$service_name is not responding correctly"
        return 1
    fi
}

# Check Docker status
print_status "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running"
    exit 1
else
    print_success "Docker is running"
fi

echo ""
print_status "=== Container Status ==="

# Check for running containers
RUNNING_CONTAINERS=$(docker ps --filter "name=diagnosis" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")

if [ -z "$RUNNING_CONTAINERS" ] || [ "$RUNNING_CONTAINERS" = "NAMES	STATUS	PORTS" ]; then
    print_warning "No diagnosis containers are running"
    echo ""
    print_status "To start the services, run:"
    echo "  ./docker-scripts/start.sh --dev    # For development"
    echo "  ./docker-scripts/start.sh --prod   # For production"
    exit 0
else
    echo "$RUNNING_CONTAINERS"
fi

echo ""
print_status "=== Service Health Checks ==="

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

API_PORT=${API_PORT:-5000}
DB_PORT=${DB_PORT:-5444}

# Check API health
if check_service_health "Diagnosis API" "http://localhost:$API_PORT/api/health"; then
    # Get detailed API status
    API_STATUS=$(curl -s "http://localhost:$API_PORT/api/health" 2>/dev/null || echo "{}")
    if [ "$API_STATUS" != "{}" ]; then
        echo "  Database diagnoses: $(echo $API_STATUS | grep -o '"database_diagnoses":[0-9]*' | cut -d':' -f2)"
        echo "  Active sessions: $(echo $API_STATUS | grep -o '"active_sessions":[0-9]*' | cut -d':' -f2)"
    fi
fi

# Check PostgreSQL
print_status "Checking PostgreSQL..."
if docker exec diagnosis_postgres pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-kforce} > /dev/null 2>&1; then
    print_success "PostgreSQL is healthy"
    
    # Get database info
    DB_INFO=$(docker exec diagnosis_postgres psql -U ${DB_USER:-postgres} -d ${DB_NAME:-kforce} -t -c "SELECT COUNT(*) FROM diagnoses;" 2>/dev/null || echo "0")
    echo "  Diagnoses in database: $(echo $DB_INFO | tr -d ' ')"
else
    print_error "PostgreSQL is not responding"
fi

# Check Redis
print_status "Checking Redis..."
if docker exec diagnosis_redis redis-cli ping > /dev/null 2>&1; then
    print_success "Redis is healthy"
else
    print_warning "Redis is not responding (this is optional)"
fi

echo ""
print_status "=== Resource Usage ==="

# Show resource usage for diagnosis containers
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker ps --filter "name=diagnosis" -q) 2>/dev/null || print_warning "No containers to show stats for"

echo ""
print_status "=== Quick Commands ==="
echo "View logs:     ./docker-scripts/logs.sh --api -f"
echo "Stop services: ./docker-scripts/stop.sh"
echo "Restart:       ./docker-scripts/stop.sh && ./docker-scripts/start.sh"
echo "API URL:       http://localhost:$API_PORT/api/health"
