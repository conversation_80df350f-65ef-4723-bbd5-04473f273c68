#!/bin/bash
# Stop the diagnosis API Docker containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
REMOVE_VOLUMES=""
REMOVE_IMAGES=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --volumes)
            REMOVE_VOLUMES="--volumes"
            shift
            ;;
        --images)
            REMOVE_IMAGES="--rmi all"
            shift
            ;;
        --clean)
            REMOVE_VOLUMES="--volumes"
            REMOVE_IMAGES="--rmi all"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --volumes              Remove volumes (database data will be lost)"
            echo "  --images               Remove built images"
            echo "  --clean                Remove both volumes and images"
            echo "  -h, --help             Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Stopping Diagnosis API containers..."

# Stop production containers
if [ -f docker-compose.yml ]; then
    print_status "Stopping production containers..."
    docker-compose -f docker-compose.yml down $REMOVE_VOLUMES $REMOVE_IMAGES
fi

# Stop development containers
if [ -f docker-compose.dev.yml ]; then
    print_status "Stopping development containers..."
    docker-compose -f docker-compose.dev.yml down $REMOVE_VOLUMES $REMOVE_IMAGES
fi

# Clean up orphaned containers
print_status "Cleaning up orphaned containers..."
docker container prune -f > /dev/null 2>&1 || true

if [ -n "$REMOVE_VOLUMES" ]; then
    print_status "Removing volumes..."
    docker volume prune -f > /dev/null 2>&1 || true
fi

if [ -n "$REMOVE_IMAGES" ]; then
    print_status "Removing images..."
    docker image prune -f > /dev/null 2>&1 || true
fi

print_success "All containers stopped successfully!"

# Show remaining containers (if any)
RUNNING_CONTAINERS=$(docker ps -q --filter "name=diagnosis")
if [ -n "$RUNNING_CONTAINERS" ]; then
    print_status "Remaining diagnosis containers:"
    docker ps --filter "name=diagnosis"
else
    print_success "No diagnosis containers are running"
fi
