#!/bin/bash
# Start the diagnosis API with Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from template..."
    cat > .env << EOF
# Database Configuration
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# API Configuration
API_PORT=5000
FLASK_DEBUG=false

# API Keys (replace with your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
XAI_API_KEY=your_xai_api_key_here

# Embedding Configuration
EMBEDDING_MODEL=text-embedding-3-small
EOF
    print_warning "Please edit .env file with your actual API keys before starting"
    exit 1
fi

# Parse command line arguments
MODE="production"
DETACHED=""
BUILD=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --dev|--development)
            MODE="development"
            shift
            ;;
        --prod|--production)
            MODE="production"
            shift
            ;;
        -d|--detached)
            DETACHED="-d"
            shift
            ;;
        --build)
            BUILD="--build"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --dev, --development    Start in development mode"
            echo "  --prod, --production    Start in production mode (default)"
            echo "  -d, --detached         Run in detached mode"
            echo "  --build                Force rebuild of images"
            echo "  -h, --help             Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Starting Diagnosis API in $MODE mode..."

# Choose the appropriate docker-compose file
if [ "$MODE" = "development" ]; then
    COMPOSE_FILE="docker-compose.dev.yml"
    print_status "Using development configuration with hot reloading"
else
    COMPOSE_FILE="docker-compose.yml"
    print_status "Using production configuration"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f $COMPOSE_FILE down > /dev/null 2>&1 || true

# Start the services
print_status "Starting services..."
if [ "$MODE" = "production" ]; then
    # Production mode - start all services except nginx by default
    docker-compose -f $COMPOSE_FILE up $DETACHED $BUILD postgres redis diagnosis_api
else
    # Development mode
    docker-compose -f $COMPOSE_FILE up $DETACHED $BUILD
fi

if [ -z "$DETACHED" ]; then
    print_success "Services started successfully!"
else
    print_success "Services started in detached mode!"
    print_status "Check status with: docker-compose -f $COMPOSE_FILE ps"
    print_status "View logs with: docker-compose -f $COMPOSE_FILE logs -f"
    print_status "Stop services with: docker-compose -f $COMPOSE_FILE down"
fi

print_status "API will be available at: http://localhost:${API_PORT:-5000}"
print_status "Health check: http://localhost:${API_PORT:-5000}/api/health"
