#!/bin/bash
# View logs from diagnosis API containers

set -e

# Colors for output
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Parse command line arguments
SERVICE=""
FOLLOW=""
TAIL="100"
COMPOSE_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --api)
            SERVICE="diagnosis_api"
            shift
            ;;
        --postgres|--db)
            SERVICE="postgres"
            shift
            ;;
        --redis)
            SERVICE="redis"
            shift
            ;;
        --nginx)
            SERVICE="nginx"
            shift
            ;;
        --dev|--development)
            COMPOSE_FILE="docker-compose.dev.yml"
            shift
            ;;
        --prod|--production)
            COMPOSE_FILE="docker-compose.yml"
            shift
            ;;
        -f|--follow)
            FOLLOW="-f"
            shift
            ;;
        --tail)
            TAIL="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --api                  Show API logs"
            echo "  --postgres, --db       Show PostgreSQL logs"
            echo "  --redis                Show Redis logs"
            echo "  --nginx                Show Nginx logs"
            echo "  --dev, --development   Use development compose file"
            echo "  --prod, --production   Use production compose file"
            echo "  -f, --follow           Follow log output"
            echo "  --tail N               Show last N lines (default: 100)"
            echo "  -h, --help             Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --api -f            Follow API logs"
            echo "  $0 --db --tail 50      Show last 50 database log lines"
            echo "  $0 --dev -f            Follow all development logs"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Auto-detect compose file if not specified
if [ -z "$COMPOSE_FILE" ]; then
    if docker-compose -f docker-compose.dev.yml ps > /dev/null 2>&1; then
        COMPOSE_FILE="docker-compose.dev.yml"
        print_status "Auto-detected development environment"
    elif docker-compose -f docker-compose.yml ps > /dev/null 2>&1; then
        COMPOSE_FILE="docker-compose.yml"
        print_status "Auto-detected production environment"
    else
        echo "No running containers found. Please start the services first."
        exit 1
    fi
fi

# Show logs
if [ -n "$SERVICE" ]; then
    print_status "Showing logs for $SERVICE (last $TAIL lines)..."
    docker-compose -f $COMPOSE_FILE logs --tail=$TAIL $FOLLOW $SERVICE
else
    print_status "Showing logs for all services (last $TAIL lines)..."
    docker-compose -f $COMPOSE_FILE logs --tail=$TAIL $FOLLOW
fi
