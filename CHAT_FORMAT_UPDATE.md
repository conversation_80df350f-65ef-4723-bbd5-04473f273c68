# Chat Format Conversation History Update

## Overview

Updated the conversation history JSON format to match the chat interface structure you provided, with `role`, `content`, `id`, `createdAt`, and `parts` fields instead of the simple step-based format.

## New Chat Format Structure

### Before (Simple Format):
```json
[
  {
    "step": 1,
    "type": "initial_symptoms",
    "content": "The cow has reduced milk production and seems lethargic"
  },
  {
    "step": 2,
    "type": "question",
    "content": "Is the udder swollen?",
    "question_number": 1
  },
  {
    "step": 3,
    "type": "answer", 
    "content": "Yes, it's swollen",
    "question_number": 1
  }
]
```

### After (Chat Format):
```json
[
  {
    "role": "user",
    "content": "high fever, inability to walk, 102F and 464k",
    "id": "2fa751a4-2f71-44dd-99b8-5902da15bbf3",
    "createdAt": "2025-07-30T05:43:21.340Z",
    "parts": [
      {
        "type": "text",
        "text": "high fever, inability to walk, 102F and 464k"
      }
    ]
  },
  {
    "role": "assistant",
    "content": "Has there been any noticeable change in the appearance of the milk?",
    "id": "f0d36b27-e4db-463e-a7eb-2fbddba48599", 
    "createdAt": "2025-07-30T05:43:28.226Z",
    "parts": [
      {
        "type": "step-start"
      },
      {
        "type": "text",
        "text": "Has there been any noticeable change in the appearance of the milk?"
      }
    ]
  },
  {
    "role": "user",
    "content": "yes, there is discolloration of milk",
    "id": "cccd9330-effe-45d8-8900-d02fc3992b82",
    "createdAt": "2025-07-30T05:43:42.133Z",
    "parts": [
      {
        "type": "text",
        "text": "yes, there is discolloration of milk"
      }
    ]
  }
]
```

## Key Changes Made

### 1. Chat Message Structure
Each conversation entry now follows the chat interface format:
- **`role`**: "user" or "assistant"
- **`content`**: The actual message content
- **`id`**: Unique UUID for each message
- **`createdAt`**: ISO timestamp with Z suffix
- **`parts`**: Array of message parts with type and text

### 2. Role Mapping
- **Initial symptoms** → `role: "user"`
- **Questions** → `role: "assistant"` with `"step-start"` part
- **Answers** → `role: "user"`

### 3. Parts Structure
- **User messages**: Single text part
- **Assistant messages**: `step-start` part + text part (matching your example)

### 4. Enhanced Symptoms Summary
Updated the symptoms extraction to handle both formats:
- **Chat format**: Extracts from `role: "user"` messages
- **Simple format**: Extracts from `type: "initial_symptoms"` and `type: "answer"`

## Files Updated

### 1. `diagnosis_chat.py`
- Updated both `GroqLLM` and `OpenAILLM` classes
- Added chat format conversion in `generate_follow_up_question()`
- Enhanced `_extract_symptoms_summary()` to handle both formats
- Generates UUIDs and timestamps for chat messages

### 2. `diagnosis_api.py`
- Added `convert_to_chat_format()` function
- Updated `process_symptoms_for_session()` to use chat format
- Enhanced symptoms summary extraction for chat format
- Maintains backward compatibility with simple format

### 3. `test_conversation_history.py`
- Updated test examples to use chat format
- Shows real conversation structure matching your example
- Demonstrates both formats working together

## Example Prompt Output

```
You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: high fever, inability to walk, 102F and 464k. Additional findings: yes, there is discolloration of milk

Conversation history (JSON format):
[
  {
    "role": "user",
    "content": "high fever, inability to walk, 102F and 464k",
    "id": "2fa751a4-2f71-44dd-99b8-5902da15bbf3",
    "createdAt": "2025-07-30T05:43:21.340Z",
    "parts": [
      {
        "type": "text",
        "text": "high fever, inability to walk, 102F and 464k"
      }
    ]
  },
  {
    "role": "assistant",
    "content": "Has there been any noticeable change in the appearance of the milk?",
    "id": "f0d36b27-e4db-463e-a7eb-2fbddba48599",
    "createdAt": "2025-07-30T05:43:28.226Z",
    "parts": [
      {
        "type": "step-start"
      },
      {
        "type": "text",
        "text": "Has there been any noticeable change in the appearance of the milk?"
      }
    ]
  },
  {
    "role": "user",
    "content": "yes, there is discolloration of milk",
    "id": "cccd9330-effe-45d8-8900-d02fc3992b82",
    "createdAt": "2025-07-30T05:43:42.133Z",
    "parts": [
      {
        "type": "text",
        "text": "yes, there is discolloration of milk"
      }
    ]
  }
]

Top potential diagnoses:
- Sub Acute Mastitis (confidence: 0.61): Milk discoloration visible...
- Chronic Fibrosis Mastitis (confidence: 0.58): Atrophied or fibrotic udder...
```

## Benefits

### 1. **Consistency with Chat Interface**
- Matches exactly the format used in your chat system
- Seamless integration with existing chat infrastructure
- Familiar structure for developers working with chat data

### 2. **Rich Metadata**
- Unique IDs for message tracking
- Timestamps for conversation timeline
- Structured parts for complex message types

### 3. **Backward Compatibility**
- Still supports simple format internally
- Gradual migration possible
- No breaking changes to existing functionality

### 4. **Enhanced AI Context**
- Better understanding of conversation flow
- Clear role identification (user vs assistant)
- Structured message parts for complex interactions

## Usage

The system now automatically converts internal conversation tracking to chat format when sending to the LLM, providing richer context while maintaining the clean symptoms summary and all existing functionality.

Both the terminal chat and REST API now generate conversation history in this chat format, making it consistent with your existing chat interface structure.
