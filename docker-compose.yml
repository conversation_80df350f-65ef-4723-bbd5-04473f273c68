version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: diagnosis_postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-kforce}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-kforce}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "${DB_PORT:-5444}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - diagnosis_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-kforce}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for session storage (optional, for production)
  redis:
    image: redis:7-alpine
    container_name: diagnosis_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - diagnosis_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Diagnosis API
  diagnosis_api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: diagnosis_api
    environment:
      # Database configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-kforce}
      DB_USER: ${DB_USER:-postgres}
      DB_PASS: ${DB_PASS:-kforce}
      
      # API configuration
      API_PORT: 5000
      FLASK_DEBUG: ${FLASK_DEBUG:-false}
      
      # API Keys (from .env file)
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      XAI_API_KEY: ${XAI_API_KEY}
      
      # Optional Redis configuration
      REDIS_URL: redis://redis:6379/0
      
      # Embedding configuration
      EMBEDDING_MODEL: ${EMBEDDING_MODEL:-text-embedding-3-small}
    ports:
      - "${API_PORT:-5000}:5000"
    volumes:
      - ./logs:/app/logs
    networks:
      - diagnosis_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: diagnosis_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - diagnosis_network
    depends_on:
      - diagnosis_api
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  diagnosis_network:
    driver: bridge
