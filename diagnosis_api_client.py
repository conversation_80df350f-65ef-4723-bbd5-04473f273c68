#!/usr/bin/env python3
"""
Example client for the Diagnosis API
Demonstrates how to interact with the REST API endpoints
"""

import requests
import json
import time
from typing import Dict, Optional

class DiagnosisAPIClient:
    """Client for interacting with the Diagnosis API"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session_id = None
    
    def health_check(self) -> Dict:
        """Check API health status"""
        try:
            response = requests.get(f"{self.base_url}/api/health")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Health check failed: {e}"}
    
    def start_chat(self, initial_symptoms: str) -> Dict:
        """Start a new chat session with initial symptoms"""
        try:
            payload = {"symptoms": initial_symptoms}
            response = requests.post(
                f"{self.base_url}/api/chat/start",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            
            result = response.json()
            if 'session_id' in result:
                self.session_id = result['session_id']
            
            return result
        except requests.RequestException as e:
            return {"error": f"Failed to start chat: {e}"}
    
    def answer_question(self, answer: str) -> Dict:
        """Answer a follow-up question"""
        if not self.session_id:
            return {"error": "No active session. Start a chat first."}
        
        try:
            payload = {"answer": answer}
            response = requests.post(
                f"{self.base_url}/api/chat/{self.session_id}/answer",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Failed to answer question: {e}"}
    
    def get_status(self) -> Dict:
        """Get current chat session status"""
        if not self.session_id:
            return {"error": "No active session. Start a chat first."}
        
        try:
            response = requests.get(f"{self.base_url}/api/chat/{self.session_id}/status")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Failed to get status: {e}"}

def print_response(response: Dict, title: str = "Response"):
    """Pretty print API response"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(json.dumps(response, indent=2))

def interactive_demo():
    """Interactive demo of the API"""
    client = DiagnosisAPIClient()
    
    print("🏥 Medical Diagnosis API Client Demo")
    print("="*50)
    
    # Health check
    print("\n1. Checking API health...")
    health = client.health_check()
    print_response(health, "Health Check")
    
    if 'error' in health:
        print("❌ API is not healthy. Please start the server first.")
        return
    
    # Start chat
    print("\n2. Starting chat session...")
    initial_symptoms = input("Enter initial symptoms: ").strip()
    
    if not initial_symptoms:
        print("❌ No symptoms provided. Exiting.")
        return
    
    start_result = client.start_chat(initial_symptoms)
    print_response(start_result, "Chat Started")
    
    if 'error' in start_result:
        print("❌ Failed to start chat. Exiting.")
        return
    
    # Interactive question-answer loop
    while True:
        if start_result.get('type') == 'final_diagnosis':
            print("\n🎯 Final diagnosis reached!")
            diagnosis = start_result.get('diagnosis', {})
            print(f"Diagnosis: {diagnosis.get('name', 'Unknown')}")
            print(f"Confidence: {diagnosis.get('confidence_score', 0) * 100:.1f}%")
            print(f"Description: {diagnosis.get('description', 'N/A')}")
            break
        
        elif start_result.get('type') == 'follow_up_question':
            question = start_result.get('question', '')
            question_num = start_result.get('question_number', 0)
            max_questions = start_result.get('max_questions', 5)
            
            print(f"\n🩺 Follow-up Question {question_num}/{max_questions}:")
            print(f"   {question}")
            
            # Show current top diagnoses
            top_diagnoses = start_result.get('top_diagnoses', [])
            if top_diagnoses:
                print(f"\n📊 Current top diagnoses:")
                for i, diag in enumerate(top_diagnoses[:3], 1):
                    confidence = diag.get('confidence_score', 0) * 100
                    print(f"   {i}. {diag.get('name', 'Unknown')} ({confidence:.1f}%)")
            
            # Get user answer
            answer = input(f"\n👤 Your answer (or 'quit' to exit): ").strip()
            
            if answer.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not answer:
                print("Please provide an answer.")
                continue
            
            # Send answer
            answer_result = client.answer_question(answer)
            print_response(answer_result, "Answer Processed")
            
            if 'error' in answer_result:
                print("❌ Error processing answer. Exiting.")
                break
            
            start_result = answer_result  # Update for next iteration
        
        elif start_result.get('type') == 'no_matches':
            print("\n❌ No matching diagnoses found.")
            print("Please consult a medical professional.")
            break
        
        else:
            print(f"\n❓ Unexpected response type: {start_result.get('type', 'unknown')}")
            break

def batch_demo():
    """Batch demo with predefined responses"""
    client = DiagnosisAPIClient()
    
    print("🏥 Medical Diagnosis API Batch Demo")
    print("="*50)
    
    # Health check
    health = client.health_check()
    print_response(health, "Health Check")
    
    if 'error' in health:
        print("❌ API is not healthy. Please start the server first.")
        return
    
    # Predefined scenario
    initial_symptoms = "The cow has reduced milk production and seems lethargic"
    responses = [
        "Yes, the udder feels warm and swollen",
        "Yes, there's some discharge from the udder",
        "The cow has a slight fever",
        "No, the cow is still eating normally",
        "The symptoms started about 2 days ago"
    ]
    
    print(f"\nStarting chat with: {initial_symptoms}")
    result = client.start_chat(initial_symptoms)
    print_response(result, "Chat Started")
    
    if 'error' in result:
        return
    
    # Process responses
    for i, response in enumerate(responses):
        if result.get('type') == 'final_diagnosis':
            print("\n🎯 Final diagnosis reached!")
            break
        
        if result.get('type') == 'follow_up_question':
            question = result.get('question', '')
            print(f"\nQuestion: {question}")
            print(f"Response: {response}")
            
            result = client.answer_question(response)
            print_response(result, f"Response {i+1} Processed")
            
            if 'error' in result:
                break
        else:
            break
    
    # Final status
    status = client.get_status()
    print_response(status, "Final Status")

def main():
    """Main function"""
    print("Choose demo mode:")
    print("1. Interactive demo")
    print("2. Batch demo")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        interactive_demo()
    elif choice == "2":
        batch_demo()
    else:
        print("Invalid choice. Running interactive demo.")
        interactive_demo()

if __name__ == "__main__":
    main()
