2025-07-25 11:15:56,890 - __main__ - INFO - Initializing SymptomEmbeddingPipeline with embedder: openai
2025-07-25 11:15:56,890 - __main__ - INFO - Initialized OpenAIEmbedder with model: text-embedding-3-small
2025-07-25 11:15:56,890 - __main__ - INFO - Using individual DB config: localhost:5444
2025-07-25 11:15:56,890 - __main__ - INFO - SymptomEmbeddingPipeline initialized successfully
2025-07-25 11:15:56,890 - __main__ - INFO - Processing PDF folder: pdf
2025-07-25 11:15:56,890 - __main__ - INFO - Creating diagnoses table...
2025-07-25 11:15:56,986 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:15:57,001 - __main__ - INFO - Diagnoses table created successfully!
2025-07-25 11:15:57,074 - __main__ - INFO - Found 13099 PDF files
2025-07-25 11:15:57,074 - __main__ - INFO - Processing file 1/13099: 172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,074 - __main__ - INFO - Processing PDF: 172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,074 - __main__ - INFO - Extracting text from PDF: pdf/172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,085 - __main__ - INFO - Successfully extracted 509 characters from pdf/172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,085 - __main__ - INFO - Parsing medical document: 172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,086 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000185
2025-07-25 11:15:57,086 - __main__ - WARNING - No symptoms found in 172291478296198c54380f7405634.pdf
2025-07-25 11:15:57,086 - __main__ - INFO - Processing file 2/13099: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,087 - __main__ - INFO - Processing PDF: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,087 - __main__ - INFO - Extracting text from PDF: pdf/17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,103 - __main__ - INFO - Successfully extracted 1082 characters from pdf/17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,103 - __main__ - INFO - Parsing medical document: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,103 - __main__ - INFO - Parsed document with 0 symptoms for Sr000203
2025-07-25 11:15:57,103 - __main__ - WARNING - No symptoms found in 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:15:57,104 - __main__ - INFO - Processing file 3/13099: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,104 - __main__ - INFO - Processing PDF: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,104 - __main__ - INFO - Extracting text from PDF: pdf/17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,113 - __main__ - INFO - Successfully extracted 999 characters from pdf/17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,113 - __main__ - INFO - Parsing medical document: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,114 - __main__ - INFO - Parsed document with 0 symptoms for Sr021085
2025-07-25 11:15:57,114 - __main__ - WARNING - No symptoms found in 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:15:57,114 - __main__ - INFO - Processing file 4/13099: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,114 - __main__ - INFO - Processing PDF: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,114 - __main__ - INFO - Extracting text from PDF: pdf/17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Successfully extracted 841 characters from pdf/17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Parsing medical document: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Parsed document with 0 symptoms for Op2 - 030 - M Deepa
2025-07-25 11:15:57,119 - __main__ - WARNING - No symptoms found in 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Processing file 5/13099: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Processing PDF: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,119 - __main__ - INFO - Extracting text from PDF: pdf/171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,124 - __main__ - INFO - Successfully extracted 1154 characters from pdf/171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,124 - __main__ - INFO - Parsing medical document: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,125 - __main__ - INFO - Parsed document with 74 symptoms for Sr023754
2025-07-25 11:15:57,125 - __main__ - INFO - Found combined symptoms text for 171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:57,125 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:15:58,458 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:15:58,459 - __main__ - INFO - Inserting diagnosis record for Sr023754
2025-07-25 11:15:58,481 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:15:58,505 - __main__ - INFO - Successfully inserted diagnosis record for Sr023754
2025-07-25 11:15:58,505 - __main__ - INFO - Successfully processed combined symptoms from 171034083755836932f65347b3c9e.pdf
2025-07-25 11:15:58,505 - __main__ - INFO - Processing file 6/13099: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,505 - __main__ - INFO - Processing PDF: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,505 - __main__ - INFO - Extracting text from PDF: pdf/17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,514 - __main__ - INFO - Successfully extracted 636 characters from pdf/17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,514 - __main__ - INFO - Parsing medical document: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,515 - __main__ - INFO - Parsed document with 0 symptoms for Sr021377
2025-07-25 11:15:58,515 - __main__ - WARNING - No symptoms found in 17034286186462930962e797ebaa1.pdf
2025-07-25 11:15:58,515 - __main__ - INFO - Processing file 7/13099: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,515 - __main__ - INFO - Processing PDF: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,516 - __main__ - INFO - Extracting text from PDF: pdf/173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Successfully extracted 96 characters from pdf/173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Parsing medical document: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Parsed document with 0 symptoms for 006 - Lakshmamma/**********
2025-07-25 11:15:58,521 - __main__ - WARNING - No symptoms found in 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Processing file 8/13099: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Processing PDF: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,521 - __main__ - INFO - Extracting text from PDF: pdf/1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Successfully extracted 574 characters from pdf/1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Parsing medical document: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000114
2025-07-25 11:15:58,531 - __main__ - WARNING - No symptoms found in 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Processing file 9/13099: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Processing PDF: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,531 - __main__ - INFO - Extracting text from PDF: pdf/1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,548 - __main__ - INFO - Successfully extracted 972 characters from pdf/1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,549 - __main__ - INFO - Parsing medical document: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,550 - __main__ - INFO - Parsed document with 0 symptoms for Sr022388
2025-07-25 11:15:58,550 - __main__ - WARNING - No symptoms found in 1706182331250bed957ec052066e9.pdf
2025-07-25 11:15:58,550 - __main__ - INFO - Processing file 10/13099: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,550 - __main__ - INFO - Processing PDF: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,550 - __main__ - INFO - Extracting text from PDF: pdf/1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,560 - __main__ - INFO - Successfully extracted 1143 characters from pdf/1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,561 - __main__ - INFO - Parsing medical document: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,561 - __main__ - INFO - Parsed document with 74 symptoms for Sr000960
2025-07-25 11:15:58,561 - __main__ - INFO - Found combined symptoms text for 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:58,561 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:15:59,449 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:15:59,454 - __main__ - INFO - Inserting diagnosis record for Sr000960
2025-07-25 11:15:59,478 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:15:59,494 - __main__ - INFO - Successfully inserted diagnosis record for Sr000960
2025-07-25 11:15:59,494 - __main__ - INFO - Successfully processed combined symptoms from 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:15:59,494 - __main__ - INFO - Processing file 11/13099: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,495 - __main__ - INFO - Processing PDF: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,495 - __main__ - INFO - Extracting text from PDF: pdf/17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,550 - __main__ - INFO - Successfully extracted 31353 characters from pdf/17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,550 - __main__ - INFO - Parsing medical document: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,562 - __main__ - INFO - Parsed document with 55 symptoms for Sr024217
2025-07-25 11:15:59,563 - __main__ - INFO - Found combined symptoms text for 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:15:59,563 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:00,614 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:00,625 - __main__ - INFO - Inserting diagnosis record for Sr024217
2025-07-25 11:16:00,648 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:00,657 - __main__ - INFO - Successfully inserted diagnosis record for Sr024217
2025-07-25 11:16:00,658 - __main__ - INFO - Successfully processed combined symptoms from 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:16:00,658 - __main__ - INFO - Processing file 12/13099: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,658 - __main__ - INFO - Processing PDF: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,658 - __main__ - INFO - Extracting text from PDF: pdf/173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,680 - __main__ - INFO - Successfully extracted 3867 characters from pdf/173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,680 - __main__ - INFO - Parsing medical document: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,680 - __main__ - INFO - Parsed document with 0 symptoms for 173201540935361A5Ef1F29411E08
2025-07-25 11:16:00,681 - __main__ - WARNING - No symptoms found in 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:16:00,681 - __main__ - INFO - Processing file 13/13099: 172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,681 - __main__ - INFO - Processing PDF: 172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,681 - __main__ - INFO - Extracting text from PDF: pdf/172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,686 - __main__ - INFO - Successfully extracted 594 characters from pdf/172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,686 - __main__ - INFO - Parsing medical document: 172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,687 - __main__ - INFO - Parsed document with 0 symptoms for Adchkpm000008
2025-07-25 11:16:00,687 - __main__ - WARNING - No symptoms found in 172491762143979b9689594a95b91.pdf
2025-07-25 11:16:00,687 - __main__ - INFO - Processing file 14/13099: 173133169596459693ace210668da.pdf
2025-07-25 11:16:00,687 - __main__ - INFO - Processing PDF: 173133169596459693ace210668da.pdf
2025-07-25 11:16:00,687 - __main__ - INFO - Extracting text from PDF: pdf/173133169596459693ace210668da.pdf
2025-07-25 11:16:00,790 - __main__ - INFO - Successfully extracted 63730 characters from pdf/173133169596459693ace210668da.pdf
2025-07-25 11:16:00,791 - __main__ - INFO - Parsing medical document: 173133169596459693ace210668da.pdf
2025-07-25 11:16:00,809 - __main__ - INFO - Parsed document with 55 symptoms for Sr025157
2025-07-25 11:16:00,809 - __main__ - INFO - Found combined symptoms text for 173133169596459693ace210668da.pdf
2025-07-25 11:16:00,809 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:01,912 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:01,914 - __main__ - INFO - Inserting diagnosis record for Sr025157
2025-07-25 11:16:01,942 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:01,958 - __main__ - INFO - Successfully inserted diagnosis record for Sr025157
2025-07-25 11:16:01,958 - __main__ - INFO - Successfully processed combined symptoms from 173133169596459693ace210668da.pdf
2025-07-25 11:16:01,958 - __main__ - INFO - Processing file 15/13099: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,959 - __main__ - INFO - Processing PDF: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,959 - __main__ - INFO - Extracting text from PDF: pdf/170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,980 - __main__ - INFO - Successfully extracted 998 characters from pdf/170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,980 - __main__ - INFO - Parsing medical document: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,980 - __main__ - INFO - Parsed document with 74 symptoms for Sr023283
2025-07-25 11:16:01,981 - __main__ - INFO - Found combined symptoms text for 170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:01,981 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:03,129 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:03,142 - __main__ - INFO - Inserting diagnosis record for Sr023283
2025-07-25 11:16:03,168 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:03,181 - __main__ - INFO - Successfully inserted diagnosis record for Sr023283
2025-07-25 11:16:03,182 - __main__ - INFO - Successfully processed combined symptoms from 170888011951756a2933246d60d5e.pdf
2025-07-25 11:16:03,182 - __main__ - INFO - Processing file 16/13099: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,182 - __main__ - INFO - Processing PDF: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,182 - __main__ - INFO - Extracting text from PDF: pdf/1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,206 - __main__ - INFO - Successfully extracted 1002 characters from pdf/1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,206 - __main__ - INFO - Parsing medical document: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,207 - __main__ - INFO - Parsed document with 0 symptoms for Sr021411
2025-07-25 11:16:03,207 - __main__ - WARNING - No symptoms found in 1703492745206b753c659da5c275f.pdf
2025-07-25 11:16:03,207 - __main__ - INFO - Processing file 17/13099: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,207 - __main__ - INFO - Processing PDF: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,207 - __main__ - INFO - Extracting text from PDF: pdf/1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,219 - __main__ - INFO - Successfully extracted 643 characters from pdf/1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,219 - __main__ - INFO - Parsing medical document: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,219 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000033
2025-07-25 11:16:03,220 - __main__ - WARNING - No symptoms found in 1727245394623ca899c65581f9239.pdf
2025-07-25 11:16:03,220 - __main__ - INFO - Processing file 18/13099: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,220 - __main__ - INFO - Processing PDF: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,220 - __main__ - INFO - Extracting text from PDF: pdf/17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Successfully extracted 984 characters from pdf/17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Parsing medical document: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Parsed document with 0 symptoms for Sr000582
2025-07-25 11:16:03,233 - __main__ - WARNING - No symptoms found in 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Processing file 19/13099: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Processing PDF: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,233 - __main__ - INFO - Extracting text from PDF: pdf/1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,314 - __main__ - INFO - Successfully extracted 23554 characters from pdf/1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,314 - __main__ - INFO - Parsing medical document: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,330 - __main__ - INFO - Parsed document with 55 symptoms for Sr024189
2025-07-25 11:16:03,330 - __main__ - INFO - Found combined symptoms text for 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:03,330 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:04,181 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:04,185 - __main__ - INFO - Inserting diagnosis record for Sr024189
2025-07-25 11:16:04,210 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:04,229 - __main__ - INFO - Successfully inserted diagnosis record for Sr024189
2025-07-25 11:16:04,229 - __main__ - INFO - Successfully processed combined symptoms from 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:16:04,229 - __main__ - INFO - Processing file 20/13099: 1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,229 - __main__ - INFO - Processing PDF: 1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,229 - __main__ - INFO - Extracting text from PDF: pdf/1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,254 - __main__ - INFO - Successfully extracted 848 characters from pdf/1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,254 - __main__ - INFO - Parsing medical document: 1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,255 - __main__ - INFO - Parsed document with 0 symptoms for Sr021285
2025-07-25 11:16:04,255 - __main__ - WARNING - No symptoms found in 1703171288866f993788bc43f2ab0.pdf
2025-07-25 11:16:04,255 - __main__ - INFO - Processing file 21/13099: 1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,255 - __main__ - INFO - Processing PDF: 1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,255 - __main__ - INFO - Extracting text from PDF: pdf/1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,266 - __main__ - INFO - Successfully extracted 996 characters from pdf/1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,266 - __main__ - INFO - Parsing medical document: 1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,267 - __main__ - INFO - Parsed document with 74 symptoms for Sr023273
2025-07-25 11:16:04,267 - __main__ - INFO - Found combined symptoms text for 1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:04,267 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:05,058 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:05,063 - __main__ - INFO - Inserting diagnosis record for Sr023273
2025-07-25 11:16:05,095 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:05,111 - __main__ - INFO - Successfully inserted diagnosis record for Sr023273
2025-07-25 11:16:05,111 - __main__ - INFO - Successfully processed combined symptoms from 1708862886912dac177b00110b83d.pdf
2025-07-25 11:16:05,111 - __main__ - INFO - Processing file 22/13099: 1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,111 - __main__ - INFO - Processing PDF: 1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,111 - __main__ - INFO - Extracting text from PDF: pdf/1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,119 - __main__ - INFO - Successfully extracted 779 characters from pdf/1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,119 - __main__ - INFO - Parsing medical document: 1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,119 - __main__ - INFO - Parsed document with 0 symptoms for Sr022791
2025-07-25 11:16:05,120 - __main__ - WARNING - No symptoms found in 1707299012365fdc85cbd1ece7178.pdf
2025-07-25 11:16:05,120 - __main__ - INFO - Processing file 23/13099: 1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,120 - __main__ - INFO - Processing PDF: 1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,120 - __main__ - INFO - Extracting text from PDF: pdf/1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Successfully extracted 601 characters from pdf/1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Parsing medical document: 1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Parsed document with 0 symptoms for Adchkpm000025
2025-07-25 11:16:05,127 - __main__ - WARNING - No symptoms found in 1724915429352178b4d3af4a75b63.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Processing file 24/13099: 17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Processing PDF: 17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,127 - __main__ - INFO - Extracting text from PDF: pdf/17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,132 - __main__ - INFO - Successfully extracted 771 characters from pdf/17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,132 - __main__ - INFO - Parsing medical document: 17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,132 - __main__ - INFO - Parsed document with 0 symptoms for Sr023931
2025-07-25 11:16:05,133 - __main__ - WARNING - No symptoms found in 17111815321414d4dd2f77b796f9e.pdf
2025-07-25 11:16:05,133 - __main__ - INFO - Processing file 25/13099: 17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,133 - __main__ - INFO - Processing PDF: 17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,133 - __main__ - INFO - Extracting text from PDF: pdf/17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Successfully extracted 596 characters from pdf/17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Parsing medical document: 17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000123
2025-07-25 11:16:05,145 - __main__ - WARNING - No symptoms found in 17248141162804bbd86451f40c046.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Processing file 26/13099: 1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Processing PDF: 1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,145 - __main__ - INFO - Extracting text from PDF: pdf/1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,156 - __main__ - INFO - Successfully extracted 861 characters from pdf/1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,156 - __main__ - INFO - Parsing medical document: 1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,156 - __main__ - INFO - Parsed document with 0 symptoms for Sr023303
2025-07-25 11:16:05,156 - __main__ - WARNING - No symptoms found in 1709007070717cfc5726b8670036e.pdf
2025-07-25 11:16:05,156 - __main__ - INFO - Processing file 27/13099: 17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,156 - __main__ - INFO - Processing PDF: 17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,157 - __main__ - INFO - Extracting text from PDF: pdf/17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,186 - __main__ - INFO - Successfully extracted 5178 characters from pdf/17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,186 - __main__ - INFO - Parsing medical document: 17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,188 - __main__ - INFO - Parsed document with 47 symptoms for Sr024720
2025-07-25 11:16:05,188 - __main__ - INFO - Found combined symptoms text for 17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,188 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:05,922 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:05,927 - __main__ - INFO - Inserting diagnosis record for Sr024720
2025-07-25 11:16:05,954 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:05,965 - __main__ - INFO - Successfully inserted diagnosis record for Sr024720
2025-07-25 11:16:05,966 - __main__ - INFO - Successfully processed combined symptoms from 17192496578137d9ec3b601c92c1d.pdf
2025-07-25 11:16:05,966 - __main__ - INFO - Processing file 28/13099: 1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,966 - __main__ - INFO - Processing PDF: 1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,966 - __main__ - INFO - Extracting text from PDF: pdf/1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Successfully extracted 538 characters from pdf/1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Parsing medical document: 1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000303
2025-07-25 11:16:05,989 - __main__ - WARNING - No symptoms found in 1720413926369111abf902456c1b5.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Processing file 29/13099: 1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Processing PDF: 1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:05,989 - __main__ - INFO - Extracting text from PDF: pdf/1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:06,026 - __main__ - INFO - Successfully extracted 2328 characters from pdf/1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:06,026 - __main__ - INFO - Parsing medical document: 1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:06,027 - __main__ - INFO - Parsed document with 0 symptoms for Mhsomls000334
2025-07-25 11:16:06,027 - __main__ - WARNING - No symptoms found in 1735575451323f7dc4106f5fb21ed.pdf
2025-07-25 11:16:06,027 - __main__ - INFO - Processing file 30/13099: 17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,027 - __main__ - INFO - Processing PDF: 17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,027 - __main__ - INFO - Extracting text from PDF: pdf/17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,035 - __main__ - INFO - Successfully extracted 764 characters from pdf/17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,035 - __main__ - INFO - Parsing medical document: 17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,036 - __main__ - INFO - Parsed document with 0 symptoms for Sr022928
2025-07-25 11:16:06,036 - __main__ - WARNING - No symptoms found in 17077568649762ca3f41c528f91bc.pdf
2025-07-25 11:16:06,036 - __main__ - INFO - Processing file 31/13099: 1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,036 - __main__ - INFO - Processing PDF: 1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,036 - __main__ - INFO - Extracting text from PDF: pdf/1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,039 - __main__ - INFO - Successfully extracted 933 characters from pdf/1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,039 - __main__ - INFO - Parsing medical document: 1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,039 - __main__ - INFO - Parsed document with 74 symptoms for Sr022559
2025-07-25 11:16:06,039 - __main__ - INFO - Found combined symptoms text for 1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:06,039 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:06,970 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:06,974 - __main__ - INFO - Inserting diagnosis record for Sr022559
2025-07-25 11:16:06,998 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:07,010 - __main__ - INFO - Successfully inserted diagnosis record for Sr022559
2025-07-25 11:16:07,011 - __main__ - INFO - Successfully processed combined symptoms from 1706587565233206574fbb5595f4f.pdf
2025-07-25 11:16:07,011 - __main__ - INFO - Processing file 32/13099: 171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,011 - __main__ - INFO - Processing PDF: 171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,011 - __main__ - INFO - Extracting text from PDF: pdf/171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,024 - __main__ - INFO - Successfully extracted 1282 characters from pdf/171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,025 - __main__ - INFO - Parsing medical document: 171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,025 - __main__ - INFO - Parsed document with 74 symptoms for Sr000206
2025-07-25 11:16:07,025 - __main__ - INFO - Found combined symptoms text for 171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,025 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:07,493 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:07,496 - __main__ - INFO - Inserting diagnosis record for Sr000206
2025-07-25 11:16:07,519 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:07,533 - __main__ - INFO - Successfully inserted diagnosis record for Sr000206
2025-07-25 11:16:07,534 - __main__ - INFO - Successfully processed combined symptoms from 171886407379555074a2bad3c50b8.pdf
2025-07-25 11:16:07,534 - __main__ - INFO - Processing file 33/13099: 1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,534 - __main__ - INFO - Processing PDF: 1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,534 - __main__ - INFO - Extracting text from PDF: pdf/1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,542 - __main__ - INFO - Successfully extracted 763 characters from pdf/1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,542 - __main__ - INFO - Parsing medical document: 1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,543 - __main__ - INFO - Parsed document with 0 symptoms for Sr022493
2025-07-25 11:16:07,543 - __main__ - WARNING - No symptoms found in 1706516631159e2423ffab9a36b47.pdf
2025-07-25 11:16:07,543 - __main__ - INFO - Processing file 34/13099: 170732568713903948920f038d473.pdf
2025-07-25 11:16:07,543 - __main__ - INFO - Processing PDF: 170732568713903948920f038d473.pdf
2025-07-25 11:16:07,543 - __main__ - INFO - Extracting text from PDF: pdf/170732568713903948920f038d473.pdf
2025-07-25 11:16:07,550 - __main__ - INFO - Successfully extracted 1170 characters from pdf/170732568713903948920f038d473.pdf
2025-07-25 11:16:07,550 - __main__ - INFO - Parsing medical document: 170732568713903948920f038d473.pdf
2025-07-25 11:16:07,550 - __main__ - INFO - Parsed document with 74 symptoms for Sr022703
2025-07-25 11:16:07,550 - __main__ - INFO - Found combined symptoms text for 170732568713903948920f038d473.pdf
2025-07-25 11:16:07,550 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:08,562 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:08,566 - __main__ - INFO - Inserting diagnosis record for Sr022703
2025-07-25 11:16:08,586 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:08,598 - __main__ - INFO - Successfully inserted diagnosis record for Sr022703
2025-07-25 11:16:08,598 - __main__ - INFO - Successfully processed combined symptoms from 170732568713903948920f038d473.pdf
2025-07-25 11:16:08,598 - __main__ - INFO - Processing file 35/13099: 17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,598 - __main__ - INFO - Processing PDF: 17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,598 - __main__ - INFO - Extracting text from PDF: pdf/17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,611 - __main__ - INFO - Successfully extracted 892 characters from pdf/17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,611 - __main__ - INFO - Parsing medical document: 17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,611 - __main__ - INFO - Parsed document with 0 symptoms for Sr023408
2025-07-25 11:16:08,611 - __main__ - WARNING - No symptoms found in 17094632327293e3f8a69001db2da.pdf
2025-07-25 11:16:08,611 - __main__ - INFO - Processing file 36/13099: 17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,611 - __main__ - INFO - Processing PDF: 17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,612 - __main__ - INFO - Extracting text from PDF: pdf/17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,619 - __main__ - INFO - Successfully extracted 712 characters from pdf/17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,619 - __main__ - INFO - Parsing medical document: 17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,620 - __main__ - INFO - Parsed document with 0 symptoms for 17033093341003944Ff86Cbd1F8A2
2025-07-25 11:16:08,620 - __main__ - WARNING - No symptoms found in 17033093341003944ff86cbd1f8a2.pdf
2025-07-25 11:16:08,620 - __main__ - INFO - Processing file 37/13099: 1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,620 - __main__ - INFO - Processing PDF: 1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,620 - __main__ - INFO - Extracting text from PDF: pdf/1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,627 - __main__ - INFO - Successfully extracted 1187 characters from pdf/1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,627 - __main__ - INFO - Parsing medical document: 1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,628 - __main__ - INFO - Parsed document with 0 symptoms for Sr021243
2025-07-25 11:16:08,628 - __main__ - WARNING - No symptoms found in 1703089125110c68724293dac75e6.pdf
2025-07-25 11:16:08,628 - __main__ - INFO - Processing file 38/13099: 172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,628 - __main__ - INFO - Processing PDF: 172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,628 - __main__ - INFO - Extracting text from PDF: pdf/172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Successfully extracted 596 characters from pdf/172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Parsing medical document: 172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Parsed document with 0 symptoms for Adchkpm000095
2025-07-25 11:16:08,639 - __main__ - WARNING - No symptoms found in 172774384702368c5b5793962c1bf.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Processing file 39/13099: 17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Processing PDF: 17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,639 - __main__ - INFO - Extracting text from PDF: pdf/17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Successfully extracted 873 characters from pdf/17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Parsing medical document: 17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Parsed document with 0 symptoms for Sr022612
2025-07-25 11:16:08,643 - __main__ - WARNING - No symptoms found in 17068003600306d2922e592efc21f.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Processing file 40/13099: 17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Processing PDF: 17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,643 - __main__ - INFO - Extracting text from PDF: pdf/17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,702 - __main__ - INFO - Successfully extracted 27672 characters from pdf/17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,702 - __main__ - INFO - Parsing medical document: 17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,710 - __main__ - INFO - Parsed document with 55 symptoms for Sr024585
2025-07-25 11:16:08,711 - __main__ - INFO - Found combined symptoms text for 17179425975416067807a751b7b91.pdf
2025-07-25 11:16:08,711 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:09,768 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:09,771 - __main__ - INFO - Inserting diagnosis record for Sr024585
2025-07-25 11:16:09,796 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:09,806 - __main__ - INFO - Successfully inserted diagnosis record for Sr024585
2025-07-25 11:16:09,806 - __main__ - INFO - Successfully processed combined symptoms from 17179425975416067807a751b7b91.pdf
2025-07-25 11:16:09,806 - __main__ - INFO - Processing file 41/13099: 17176418718987829805006186c03.pdf
2025-07-25 11:16:09,807 - __main__ - INFO - Processing PDF: 17176418718987829805006186c03.pdf
2025-07-25 11:16:09,807 - __main__ - INFO - Extracting text from PDF: pdf/17176418718987829805006186c03.pdf
2025-07-25 11:16:09,817 - __main__ - INFO - Successfully extracted 620 characters from pdf/17176418718987829805006186c03.pdf
2025-07-25 11:16:09,818 - __main__ - INFO - Parsing medical document: 17176418718987829805006186c03.pdf
2025-07-25 11:16:09,818 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000167
2025-07-25 11:16:09,818 - __main__ - WARNING - No symptoms found in 17176418718987829805006186c03.pdf
2025-07-25 11:16:09,818 - __main__ - INFO - Processing file 42/13099: 17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,818 - __main__ - INFO - Processing PDF: 17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,818 - __main__ - INFO - Extracting text from PDF: pdf/17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,927 - __main__ - INFO - Successfully extracted 63682 characters from pdf/17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,927 - __main__ - INFO - Parsing medical document: 17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,945 - __main__ - INFO - Parsed document with 55 symptoms for Sr025145
2025-07-25 11:16:09,945 - __main__ - INFO - Found combined symptoms text for 17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:09,945 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:10,822 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:10,825 - __main__ - INFO - Inserting diagnosis record for Sr025145
2025-07-25 11:16:10,850 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:10,863 - __main__ - INFO - Successfully inserted diagnosis record for Sr025145
2025-07-25 11:16:10,864 - __main__ - INFO - Successfully processed combined symptoms from 17306220352961c4db32f6140c117.pdf
2025-07-25 11:16:10,864 - __main__ - INFO - Processing file 43/13099: 17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,864 - __main__ - INFO - Processing PDF: 17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,864 - __main__ - INFO - Extracting text from PDF: pdf/17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,871 - __main__ - INFO - Successfully extracted 985 characters from pdf/17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,871 - __main__ - INFO - Parsing medical document: 17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,872 - __main__ - INFO - Parsed document with 74 symptoms for Sr022703
2025-07-25 11:16:10,872 - __main__ - INFO - Found combined symptoms text for 17070531314534582f9baace49422.pdf
2025-07-25 11:16:10,872 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:16:11,608 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:16:11,610 - __main__ - INFO - Inserting diagnosis record for Sr022703
2025-07-25 11:16:11,630 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:16:11,641 - __main__ - INFO - Successfully inserted diagnosis record for Sr022703
2025-07-25 11:16:11,641 - __main__ - INFO - Successfully processed combined symptoms from 17070531314534582f9baace49422.pdf
2025-07-25 11:16:11,641 - __main__ - INFO - Processing file 44/13099: 1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,641 - __main__ - INFO - Processing PDF: 1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,641 - __main__ - INFO - Extracting text from PDF: pdf/1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,647 - __main__ - INFO - Successfully extracted 499 characters from pdf/1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,647 - __main__ - INFO - Parsing medical document: 1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,648 - __main__ - INFO - Parsed document with 0 symptoms for Adchkpm000161
2025-07-25 11:16:11,648 - __main__ - WARNING - No symptoms found in 1733243665878368175ed5e74e137.pdf
2025-07-25 11:16:11,648 - __main__ - INFO - Processing file 45/13099: 17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,648 - __main__ - INFO - Processing PDF: 17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,648 - __main__ - INFO - Extracting text from PDF: pdf/17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,659 - __main__ - INFO - Successfully extracted 1230 characters from pdf/17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,659 - __main__ - INFO - Parsing medical document: 17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,659 - __main__ - INFO - Parsed document with 74 symptoms for Sr000068
2025-07-25 11:16:11,659 - __main__ - INFO - Found combined symptoms text for 17172521816972cdd3869657d50fd.pdf
2025-07-25 11:16:11,659 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:15,035 - __main__ - INFO - Initializing SymptomEmbeddingPipeline with embedder: openai
2025-07-25 11:17:15,035 - __main__ - INFO - Initialized OpenAIEmbedder with model: text-embedding-3-small
2025-07-25 11:17:15,035 - __main__ - INFO - Using individual DB config: localhost:5444
2025-07-25 11:17:15,035 - __main__ - INFO - SymptomEmbeddingPipeline initialized successfully
2025-07-25 11:17:15,035 - __main__ - INFO - Processing PDF folder: pdf
2025-07-25 11:17:15,035 - __main__ - INFO - Creating diagnoses table...
2025-07-25 11:17:15,084 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:15,089 - __main__ - INFO - Diagnoses table created successfully!
2025-07-25 11:17:15,119 - __main__ - INFO - Found 13099 PDF files
2025-07-25 11:17:15,120 - __main__ - INFO - Processing file 1/13099: 172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,120 - __main__ - INFO - Processing PDF: 172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,120 - __main__ - INFO - Extracting text from PDF: pdf/172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,141 - __main__ - INFO - Successfully extracted 509 characters from pdf/172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,141 - __main__ - INFO - Parsing medical document: 172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,142 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000185
2025-07-25 11:17:15,142 - __main__ - WARNING - No symptoms found in 172291478296198c54380f7405634.pdf
2025-07-25 11:17:15,142 - __main__ - INFO - Processing file 2/13099: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,143 - __main__ - INFO - Processing PDF: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,143 - __main__ - INFO - Extracting text from PDF: pdf/17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,158 - __main__ - INFO - Successfully extracted 1082 characters from pdf/17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,158 - __main__ - INFO - Parsing medical document: 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,159 - __main__ - INFO - Parsed document with 0 symptoms for Sr000203
2025-07-25 11:17:15,159 - __main__ - WARNING - No symptoms found in 17188903252353e49319fc6ca10f4.pdf
2025-07-25 11:17:15,159 - __main__ - INFO - Processing file 3/13099: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,159 - __main__ - INFO - Processing PDF: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,159 - __main__ - INFO - Extracting text from PDF: pdf/17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Successfully extracted 999 characters from pdf/17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Parsing medical document: 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Parsed document with 0 symptoms for Sr021085
2025-07-25 11:17:15,167 - __main__ - WARNING - No symptoms found in 17032931725853a035da0c8b518f2.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Processing file 4/13099: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Processing PDF: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,167 - __main__ - INFO - Extracting text from PDF: pdf/17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,172 - __main__ - INFO - Successfully extracted 841 characters from pdf/17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,173 - __main__ - INFO - Parsing medical document: 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,173 - __main__ - INFO - Parsed document with 0 symptoms for Op2 - 030 - M Deepa
2025-07-25 11:17:15,173 - __main__ - WARNING - No symptoms found in 17329577160533633f18bb7d0057a.pdf
2025-07-25 11:17:15,173 - __main__ - INFO - Processing file 5/13099: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,173 - __main__ - INFO - Processing PDF: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,173 - __main__ - INFO - Extracting text from PDF: pdf/171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,177 - __main__ - INFO - Successfully extracted 1154 characters from pdf/171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,177 - __main__ - INFO - Parsing medical document: 171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,177 - __main__ - INFO - Parsed document with 74 symptoms for Sr023754
2025-07-25 11:17:15,177 - __main__ - INFO - Found combined symptoms text for 171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,177 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:15,809 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:17:15,811 - __main__ - INFO - Inserting diagnosis record for Sr023754
2025-07-25 11:17:15,833 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:15,851 - __main__ - INFO - Successfully inserted diagnosis record for Sr023754
2025-07-25 11:17:15,851 - __main__ - INFO - Successfully processed combined symptoms from 171034083755836932f65347b3c9e.pdf
2025-07-25 11:17:15,851 - __main__ - INFO - Processing file 6/13099: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,852 - __main__ - INFO - Processing PDF: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,852 - __main__ - INFO - Extracting text from PDF: pdf/17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,864 - __main__ - INFO - Successfully extracted 636 characters from pdf/17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,864 - __main__ - INFO - Parsing medical document: 17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,864 - __main__ - INFO - Parsed document with 0 symptoms for Sr021377
2025-07-25 11:17:15,864 - __main__ - WARNING - No symptoms found in 17034286186462930962e797ebaa1.pdf
2025-07-25 11:17:15,865 - __main__ - INFO - Processing file 7/13099: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,865 - __main__ - INFO - Processing PDF: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,865 - __main__ - INFO - Extracting text from PDF: pdf/173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Successfully extracted 96 characters from pdf/173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Parsing medical document: 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Parsed document with 0 symptoms for 006 - Lakshmamma/**********
2025-07-25 11:17:15,868 - __main__ - WARNING - No symptoms found in 173451551300098e6d004a16b30b5.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Processing file 8/13099: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Processing PDF: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,868 - __main__ - INFO - Extracting text from PDF: pdf/1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,880 - __main__ - INFO - Successfully extracted 574 characters from pdf/1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,881 - __main__ - INFO - Parsing medical document: 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,881 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000114
2025-07-25 11:17:15,881 - __main__ - WARNING - No symptoms found in 1726501890161945ce1dad48fbb36.pdf
2025-07-25 11:17:15,881 - __main__ - INFO - Processing file 9/13099: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,881 - __main__ - INFO - Processing PDF: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,881 - __main__ - INFO - Extracting text from PDF: pdf/1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Successfully extracted 972 characters from pdf/1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Parsing medical document: 1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Parsed document with 0 symptoms for Sr022388
2025-07-25 11:17:15,896 - __main__ - WARNING - No symptoms found in 1706182331250bed957ec052066e9.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Processing file 10/13099: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Processing PDF: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,896 - __main__ - INFO - Extracting text from PDF: pdf/1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,906 - __main__ - INFO - Successfully extracted 1143 characters from pdf/1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,906 - __main__ - INFO - Parsing medical document: 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,906 - __main__ - INFO - Parsed document with 74 symptoms for Sr000960
2025-07-25 11:17:15,906 - __main__ - INFO - Found combined symptoms text for 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:15,906 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:16,707 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:17:16,711 - __main__ - INFO - Inserting diagnosis record for Sr000960
2025-07-25 11:17:16,735 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:16,747 - __main__ - INFO - Successfully inserted diagnosis record for Sr000960
2025-07-25 11:17:16,747 - __main__ - INFO - Successfully processed combined symptoms from 1723290134697299b4ff077aaf8d7.pdf
2025-07-25 11:17:16,747 - __main__ - INFO - Processing file 11/13099: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,747 - __main__ - INFO - Processing PDF: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,747 - __main__ - INFO - Extracting text from PDF: pdf/17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,810 - __main__ - INFO - Successfully extracted 31353 characters from pdf/17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,810 - __main__ - INFO - Parsing medical document: 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,821 - __main__ - INFO - Parsed document with 55 symptoms for Sr024217
2025-07-25 11:17:16,821 - __main__ - INFO - Found combined symptoms text for 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:16,821 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:17,570 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:17:17,572 - __main__ - INFO - Inserting diagnosis record for Sr024217
2025-07-25 11:17:17,607 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:17,619 - __main__ - INFO - Successfully inserted diagnosis record for Sr024217
2025-07-25 11:17:17,619 - __main__ - INFO - Successfully processed combined symptoms from 17146654627354e4eacdd14062c11.pdf
2025-07-25 11:17:17,619 - __main__ - INFO - Processing file 12/13099: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,619 - __main__ - INFO - Processing PDF: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,619 - __main__ - INFO - Extracting text from PDF: pdf/173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,640 - __main__ - INFO - Successfully extracted 3867 characters from pdf/173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,640 - __main__ - INFO - Parsing medical document: 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,641 - __main__ - INFO - Parsed document with 0 symptoms for 173201540935361A5Ef1F29411E08
2025-07-25 11:17:17,641 - __main__ - WARNING - No symptoms found in 173201540935361a5ef1f29411e08.pdf
2025-07-25 11:17:17,641 - __main__ - INFO - Processing file 13/13099: 172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,641 - __main__ - INFO - Processing PDF: 172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,641 - __main__ - INFO - Extracting text from PDF: pdf/172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,645 - __main__ - INFO - Successfully extracted 594 characters from pdf/172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,645 - __main__ - INFO - Parsing medical document: 172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,646 - __main__ - INFO - Parsed document with 0 symptoms for Adchkpm000008
2025-07-25 11:17:17,646 - __main__ - WARNING - No symptoms found in 172491762143979b9689594a95b91.pdf
2025-07-25 11:17:17,646 - __main__ - INFO - Processing file 14/13099: 173133169596459693ace210668da.pdf
2025-07-25 11:17:17,646 - __main__ - INFO - Processing PDF: 173133169596459693ace210668da.pdf
2025-07-25 11:17:17,646 - __main__ - INFO - Extracting text from PDF: pdf/173133169596459693ace210668da.pdf
2025-07-25 11:17:17,742 - __main__ - INFO - Successfully extracted 63730 characters from pdf/173133169596459693ace210668da.pdf
2025-07-25 11:17:17,743 - __main__ - INFO - Parsing medical document: 173133169596459693ace210668da.pdf
2025-07-25 11:17:17,761 - __main__ - INFO - Parsed document with 55 symptoms for Sr025157
2025-07-25 11:17:17,762 - __main__ - INFO - Found combined symptoms text for 173133169596459693ace210668da.pdf
2025-07-25 11:17:17,762 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:18,333 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:17:18,336 - __main__ - INFO - Inserting diagnosis record for Sr025157
2025-07-25 11:17:18,358 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:18,374 - __main__ - INFO - Successfully inserted diagnosis record for Sr025157
2025-07-25 11:17:18,375 - __main__ - INFO - Successfully processed combined symptoms from 173133169596459693ace210668da.pdf
2025-07-25 11:17:18,375 - __main__ - INFO - Processing file 15/13099: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,375 - __main__ - INFO - Processing PDF: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,375 - __main__ - INFO - Extracting text from PDF: pdf/170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,404 - __main__ - INFO - Successfully extracted 998 characters from pdf/170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,404 - __main__ - INFO - Parsing medical document: 170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,405 - __main__ - INFO - Parsed document with 74 symptoms for Sr023283
2025-07-25 11:17:18,405 - __main__ - INFO - Found combined symptoms text for 170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:18,405 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:17:19,234 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:17:19,237 - __main__ - INFO - Inserting diagnosis record for Sr023283
2025-07-25 11:17:19,260 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:17:19,274 - __main__ - INFO - Successfully inserted diagnosis record for Sr023283
2025-07-25 11:17:19,275 - __main__ - INFO - Successfully processed combined symptoms from 170888011951756a2933246d60d5e.pdf
2025-07-25 11:17:19,275 - __main__ - INFO - Processing file 16/13099: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,275 - __main__ - INFO - Processing PDF: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,275 - __main__ - INFO - Extracting text from PDF: pdf/1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,300 - __main__ - INFO - Successfully extracted 1002 characters from pdf/1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,300 - __main__ - INFO - Parsing medical document: 1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,300 - __main__ - INFO - Parsed document with 0 symptoms for Sr021411
2025-07-25 11:17:19,300 - __main__ - WARNING - No symptoms found in 1703492745206b753c659da5c275f.pdf
2025-07-25 11:17:19,300 - __main__ - INFO - Processing file 17/13099: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,300 - __main__ - INFO - Processing PDF: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,301 - __main__ - INFO - Extracting text from PDF: pdf/1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,312 - __main__ - INFO - Successfully extracted 643 characters from pdf/1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,312 - __main__ - INFO - Parsing medical document: 1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,312 - __main__ - INFO - Parsed document with 0 symptoms for Tnkrslg000033
2025-07-25 11:17:19,312 - __main__ - WARNING - No symptoms found in 1727245394623ca899c65581f9239.pdf
2025-07-25 11:17:19,312 - __main__ - INFO - Processing file 18/13099: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,313 - __main__ - INFO - Processing PDF: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,313 - __main__ - INFO - Extracting text from PDF: pdf/17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Successfully extracted 984 characters from pdf/17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Parsing medical document: 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Parsed document with 0 symptoms for Sr000582
2025-07-25 11:17:19,326 - __main__ - WARNING - No symptoms found in 17211104099624ba86ac7cd314958.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Processing file 19/13099: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Processing PDF: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,326 - __main__ - INFO - Extracting text from PDF: pdf/1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,355 - __main__ - INFO - Successfully extracted 23554 characters from pdf/1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,355 - __main__ - INFO - Parsing medical document: 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,362 - __main__ - INFO - Parsed document with 55 symptoms for Sr024189
2025-07-25 11:17:19,362 - __main__ - INFO - Found combined symptoms text for 1713371931563b0b5e2d159603bab.pdf
2025-07-25 11:17:19,362 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:09,496 - __main__ - INFO - Initializing SymptomEmbeddingPipeline with embedder: openai
2025-07-25 11:20:09,496 - __main__ - INFO - Initialized OpenAIEmbedder with model: text-embedding-3-small
2025-07-25 11:20:09,496 - __main__ - INFO - Using individual DB config: localhost:5444
2025-07-25 11:20:09,496 - __main__ - INFO - SymptomEmbeddingPipeline initialized successfully
2025-07-25 11:20:09,496 - __main__ - INFO - Processing PDF folder: diseses
2025-07-25 11:20:09,496 - __main__ - ERROR - Folder diseses does not exist
2025-07-25 11:20:31,003 - __main__ - INFO - Initializing SymptomEmbeddingPipeline with embedder: openai
2025-07-25 11:20:31,003 - __main__ - INFO - Initialized OpenAIEmbedder with model: text-embedding-3-small
2025-07-25 11:20:31,003 - __main__ - INFO - Using individual DB config: localhost:5444
2025-07-25 11:20:31,003 - __main__ - INFO - SymptomEmbeddingPipeline initialized successfully
2025-07-25 11:20:31,003 - __main__ - INFO - Processing PDF folder: diseases
2025-07-25 11:20:31,003 - __main__ - INFO - Creating diagnoses table...
2025-07-25 11:20:31,107 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:31,128 - __main__ - INFO - Diagnoses table created successfully!
2025-07-25 11:20:31,128 - __main__ - INFO - Found 34 PDF files
2025-07-25 11:20:31,128 - __main__ - INFO - Processing file 1/34: DYSENTERY.pdf
2025-07-25 11:20:31,128 - __main__ - INFO - Processing PDF: DYSENTERY.pdf
2025-07-25 11:20:31,128 - __main__ - INFO - Extracting text from PDF: diseases/DYSENTERY.pdf
2025-07-25 11:20:31,145 - __main__ - INFO - Successfully extracted 815 characters from diseases/DYSENTERY.pdf
2025-07-25 11:20:31,145 - __main__ - INFO - Parsing medical document: DYSENTERY.pdf
2025-07-25 11:20:31,146 - __main__ - INFO - Parsed document with 678 symptoms for DYSENTERY
2025-07-25 11:20:31,146 - __main__ - INFO - Found combined symptoms text for DYSENTERY.pdf
2025-07-25 11:20:31,146 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:33,432 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:33,435 - __main__ - INFO - Inserting diagnosis record for DYSENTERY
2025-07-25 11:20:33,461 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:33,500 - __main__ - INFO - Successfully inserted diagnosis record for DYSENTERY
2025-07-25 11:20:33,500 - __main__ - INFO - Successfully processed combined symptoms from DYSENTERY.pdf
2025-07-25 11:20:33,500 - __main__ - INFO - Processing file 2/34: MILK FEVER.pdf
2025-07-25 11:20:33,500 - __main__ - INFO - Processing PDF: MILK FEVER.pdf
2025-07-25 11:20:33,500 - __main__ - INFO - Extracting text from PDF: diseases/MILK FEVER.pdf
2025-07-25 11:20:33,506 - __main__ - INFO - Successfully extracted 688 characters from diseases/MILK FEVER.pdf
2025-07-25 11:20:33,506 - __main__ - INFO - Parsing medical document: MILK FEVER.pdf
2025-07-25 11:20:33,506 - __main__ - INFO - Parsed document with 565 symptoms for MILK FEVER
2025-07-25 11:20:33,506 - __main__ - INFO - Found combined symptoms text for MILK FEVER.pdf
2025-07-25 11:20:33,506 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:34,645 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:34,649 - __main__ - INFO - Inserting diagnosis record for MILK FEVER
2025-07-25 11:20:34,674 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:34,690 - __main__ - INFO - Successfully inserted diagnosis record for MILK FEVER
2025-07-25 11:20:34,691 - __main__ - INFO - Successfully processed combined symptoms from MILK FEVER.pdf
2025-07-25 11:20:34,691 - __main__ - INFO - Processing file 3/34: Dry Period Mastitis.pdf
2025-07-25 11:20:34,691 - __main__ - INFO - Processing PDF: Dry Period Mastitis.pdf
2025-07-25 11:20:34,691 - __main__ - INFO - Extracting text from PDF: diseases/Dry Period Mastitis.pdf
2025-07-25 11:20:34,705 - __main__ - INFO - Successfully extracted 317 characters from diseases/Dry Period Mastitis.pdf
2025-07-25 11:20:34,705 - __main__ - INFO - Parsing medical document: Dry Period Mastitis.pdf
2025-07-25 11:20:34,706 - __main__ - INFO - Parsed document with 62 symptoms for Dry Period Mastitis
2025-07-25 11:20:34,706 - __main__ - INFO - Found combined symptoms text for Dry Period Mastitis.pdf
2025-07-25 11:20:34,706 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:36,068 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:36,070 - __main__ - INFO - Inserting diagnosis record for Dry Period Mastitis
2025-07-25 11:20:36,095 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:36,107 - __main__ - INFO - Successfully inserted diagnosis record for Dry Period Mastitis
2025-07-25 11:20:36,107 - __main__ - INFO - Successfully processed combined symptoms from Dry Period Mastitis.pdf
2025-07-25 11:20:36,107 - __main__ - INFO - Processing file 4/34: PNEUMONIA.pdf
2025-07-25 11:20:36,107 - __main__ - INFO - Processing PDF: PNEUMONIA.pdf
2025-07-25 11:20:36,107 - __main__ - INFO - Extracting text from PDF: diseases/PNEUMONIA.pdf
2025-07-25 11:20:36,114 - __main__ - INFO - Successfully extracted 955 characters from diseases/PNEUMONIA.pdf
2025-07-25 11:20:36,114 - __main__ - INFO - Parsing medical document: PNEUMONIA.pdf
2025-07-25 11:20:36,115 - __main__ - INFO - Parsed document with 746 symptoms for PNEUMONIA
2025-07-25 11:20:36,115 - __main__ - INFO - Found combined symptoms text for PNEUMONIA.pdf
2025-07-25 11:20:36,116 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:36,760 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:36,764 - __main__ - INFO - Inserting diagnosis record for PNEUMONIA
2025-07-25 11:20:36,789 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:36,801 - __main__ - INFO - Successfully inserted diagnosis record for PNEUMONIA
2025-07-25 11:20:36,801 - __main__ - INFO - Successfully processed combined symptoms from PNEUMONIA.pdf
2025-07-25 11:20:36,801 - __main__ - INFO - Processing file 5/34: THEILERIOSIS.pdf
2025-07-25 11:20:36,801 - __main__ - INFO - Processing PDF: THEILERIOSIS.pdf
2025-07-25 11:20:36,801 - __main__ - INFO - Extracting text from PDF: diseases/THEILERIOSIS.pdf
2025-07-25 11:20:36,812 - __main__ - INFO - Successfully extracted 2020 characters from diseases/THEILERIOSIS.pdf
2025-07-25 11:20:36,812 - __main__ - INFO - Parsing medical document: THEILERIOSIS.pdf
2025-07-25 11:20:36,813 - __main__ - INFO - Parsed document with 1675 symptoms for THEILERIOSIS
2025-07-25 11:20:36,813 - __main__ - INFO - Found combined symptoms text for THEILERIOSIS.pdf
2025-07-25 11:20:36,813 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:37,999 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:38,002 - __main__ - INFO - Inserting diagnosis record for THEILERIOSIS
2025-07-25 11:20:38,034 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:38,052 - __main__ - INFO - Successfully inserted diagnosis record for THEILERIOSIS
2025-07-25 11:20:38,052 - __main__ - INFO - Successfully processed combined symptoms from THEILERIOSIS.pdf
2025-07-25 11:20:38,052 - __main__ - INFO - Processing file 6/34: JAUNDICE.pdf
2025-07-25 11:20:38,052 - __main__ - INFO - Processing PDF: JAUNDICE.pdf
2025-07-25 11:20:38,052 - __main__ - INFO - Extracting text from PDF: diseases/JAUNDICE.pdf
2025-07-25 11:20:38,065 - __main__ - INFO - Successfully extracted 662 characters from diseases/JAUNDICE.pdf
2025-07-25 11:20:38,065 - __main__ - INFO - Parsing medical document: JAUNDICE.pdf
2025-07-25 11:20:38,066 - __main__ - INFO - Parsed document with 546 symptoms for JAUNDICE
2025-07-25 11:20:38,066 - __main__ - INFO - Found combined symptoms text for JAUNDICE.pdf
2025-07-25 11:20:38,066 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:39,501 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:39,505 - __main__ - INFO - Inserting diagnosis record for JAUNDICE
2025-07-25 11:20:39,531 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:39,545 - __main__ - INFO - Successfully inserted diagnosis record for JAUNDICE
2025-07-25 11:20:39,545 - __main__ - INFO - Successfully processed combined symptoms from JAUNDICE.pdf
2025-07-25 11:20:39,546 - __main__ - INFO - Processing file 7/34: TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,546 - __main__ - INFO - Processing PDF: TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,546 - __main__ - INFO - Extracting text from PDF: diseases/TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,553 - __main__ - INFO - Successfully extracted 854 characters from diseases/TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,554 - __main__ - INFO - Parsing medical document: TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,555 - __main__ - INFO - Parsed document with 690 symptoms for TRYPANOSOMIASIS
2025-07-25 11:20:39,555 - __main__ - INFO - Found combined symptoms text for TRYPANOSOMIASIS.pdf
2025-07-25 11:20:39,555 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:41,107 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:41,110 - __main__ - INFO - Inserting diagnosis record for TRYPANOSOMIASIS
2025-07-25 11:20:41,136 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:41,150 - __main__ - INFO - Successfully inserted diagnosis record for TRYPANOSOMIASIS
2025-07-25 11:20:41,150 - __main__ - INFO - Successfully processed combined symptoms from TRYPANOSOMIASIS.pdf
2025-07-25 11:20:41,150 - __main__ - INFO - Processing file 8/34: LISTERIOSIS.pdf
2025-07-25 11:20:41,150 - __main__ - INFO - Processing PDF: LISTERIOSIS.pdf
2025-07-25 11:20:41,150 - __main__ - INFO - Extracting text from PDF: diseases/LISTERIOSIS.pdf
2025-07-25 11:20:41,159 - __main__ - INFO - Successfully extracted 1787 characters from diseases/LISTERIOSIS.pdf
2025-07-25 11:20:41,159 - __main__ - INFO - Parsing medical document: LISTERIOSIS.pdf
2025-07-25 11:20:41,159 - __main__ - INFO - Parsed document with 1580 symptoms for LISTERIOSIS
2025-07-25 11:20:41,159 - __main__ - INFO - Found combined symptoms text for LISTERIOSIS.pdf
2025-07-25 11:20:41,159 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:42,334 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:42,335 - __main__ - INFO - Inserting diagnosis record for LISTERIOSIS
2025-07-25 11:20:42,380 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:42,396 - __main__ - INFO - Successfully inserted diagnosis record for LISTERIOSIS
2025-07-25 11:20:42,396 - __main__ - INFO - Successfully processed combined symptoms from LISTERIOSIS.pdf
2025-07-25 11:20:42,397 - __main__ - INFO - Processing file 9/34: BABESIA.pdf
2025-07-25 11:20:42,397 - __main__ - INFO - Processing PDF: BABESIA.pdf
2025-07-25 11:20:42,397 - __main__ - INFO - Extracting text from PDF: diseases/BABESIA.pdf
2025-07-25 11:20:42,406 - __main__ - INFO - Successfully extracted 1368 characters from diseases/BABESIA.pdf
2025-07-25 11:20:42,406 - __main__ - INFO - Parsing medical document: BABESIA.pdf
2025-07-25 11:20:42,408 - __main__ - INFO - Parsed document with 1019 symptoms for BABESIA
2025-07-25 11:20:42,408 - __main__ - INFO - Found combined symptoms text for BABESIA.pdf
2025-07-25 11:20:42,408 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:43,073 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:43,076 - __main__ - INFO - Inserting diagnosis record for BABESIA
2025-07-25 11:20:43,104 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:43,124 - __main__ - INFO - Successfully inserted diagnosis record for BABESIA
2025-07-25 11:20:43,124 - __main__ - INFO - Successfully processed combined symptoms from BABESIA.pdf
2025-07-25 11:20:43,124 - __main__ - INFO - Processing file 10/34: UTERINE TORSION.pdf
2025-07-25 11:20:43,124 - __main__ - INFO - Processing PDF: UTERINE TORSION.pdf
2025-07-25 11:20:43,124 - __main__ - INFO - Extracting text from PDF: diseases/UTERINE TORSION.pdf
2025-07-25 11:20:43,135 - __main__ - INFO - Successfully extracted 2220 characters from diseases/UTERINE TORSION.pdf
2025-07-25 11:20:43,136 - __main__ - INFO - Parsing medical document: UTERINE TORSION.pdf
2025-07-25 11:20:43,136 - __main__ - INFO - Parsed document with 1718 symptoms for UTERINE TORSION
2025-07-25 11:20:43,136 - __main__ - INFO - Found combined symptoms text for UTERINE TORSION.pdf
2025-07-25 11:20:43,136 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:44,284 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:44,287 - __main__ - INFO - Inserting diagnosis record for UTERINE TORSION
2025-07-25 11:20:44,311 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:44,332 - __main__ - INFO - Successfully inserted diagnosis record for UTERINE TORSION
2025-07-25 11:20:44,332 - __main__ - INFO - Successfully processed combined symptoms from UTERINE TORSION.pdf
2025-07-25 11:20:44,332 - __main__ - INFO - Processing file 11/34: Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,332 - __main__ - INFO - Processing PDF: Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,332 - __main__ - INFO - Extracting text from PDF: diseases/Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,337 - __main__ - INFO - Successfully extracted 444 characters from diseases/Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,337 - __main__ - INFO - Parsing medical document: Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,338 - __main__ - INFO - Parsed document with 97 symptoms for Chronic Fibrosis Mastitis
2025-07-25 11:20:44,338 - __main__ - INFO - Found combined symptoms text for Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:44,338 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:45,462 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:45,467 - __main__ - INFO - Inserting diagnosis record for Chronic Fibrosis Mastitis
2025-07-25 11:20:45,494 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:45,503 - __main__ - INFO - Successfully inserted diagnosis record for Chronic Fibrosis Mastitis
2025-07-25 11:20:45,503 - __main__ - INFO - Successfully processed combined symptoms from Chronic_Fibrosis Mastitis.pdf
2025-07-25 11:20:45,503 - __main__ - INFO - Processing file 12/34: _NAVAL ILL.pdf
2025-07-25 11:20:45,503 - __main__ - INFO - Processing PDF: _NAVAL ILL.pdf
2025-07-25 11:20:45,503 - __main__ - INFO - Extracting text from PDF: diseases/_NAVAL ILL.pdf
2025-07-25 11:20:45,507 - __main__ - INFO - Successfully extracted 1101 characters from diseases/_NAVAL ILL.pdf
2025-07-25 11:20:45,507 - __main__ - INFO - Parsing medical document: _NAVAL ILL.pdf
2025-07-25 11:20:45,507 - __main__ - INFO - Parsed document with 932 symptoms for NAVAL ILL
2025-07-25 11:20:45,507 - __main__ - INFO - Found combined symptoms text for _NAVAL ILL.pdf
2025-07-25 11:20:45,507 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:46,122 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:46,125 - __main__ - INFO - Inserting diagnosis record for NAVAL ILL
2025-07-25 11:20:46,151 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:46,164 - __main__ - INFO - Successfully inserted diagnosis record for NAVAL ILL
2025-07-25 11:20:46,165 - __main__ - INFO - Successfully processed combined symptoms from _NAVAL ILL.pdf
2025-07-25 11:20:46,165 - __main__ - INFO - Processing file 13/34: Subclinical Mastitis.pdf
2025-07-25 11:20:46,165 - __main__ - INFO - Processing PDF: Subclinical Mastitis.pdf
2025-07-25 11:20:46,165 - __main__ - INFO - Extracting text from PDF: diseases/Subclinical Mastitis.pdf
2025-07-25 11:20:46,169 - __main__ - INFO - Successfully extracted 675 characters from diseases/Subclinical Mastitis.pdf
2025-07-25 11:20:46,169 - __main__ - INFO - Parsing medical document: Subclinical Mastitis.pdf
2025-07-25 11:20:46,169 - __main__ - INFO - Parsed document with 128 symptoms for Subclinical Mastitis
2025-07-25 11:20:46,169 - __main__ - INFO - Found combined symptoms text for Subclinical Mastitis.pdf
2025-07-25 11:20:46,169 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:47,302 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:47,305 - __main__ - INFO - Inserting diagnosis record for Subclinical Mastitis
2025-07-25 11:20:47,356 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:47,378 - __main__ - INFO - Successfully inserted diagnosis record for Subclinical Mastitis
2025-07-25 11:20:47,378 - __main__ - INFO - Successfully processed combined symptoms from Subclinical Mastitis.pdf
2025-07-25 11:20:47,378 - __main__ - INFO - Processing file 14/34: ACIDOSIS.pdf
2025-07-25 11:20:47,378 - __main__ - INFO - Processing PDF: ACIDOSIS.pdf
2025-07-25 11:20:47,378 - __main__ - INFO - Extracting text from PDF: diseases/ACIDOSIS.pdf
2025-07-25 11:20:47,386 - __main__ - INFO - Successfully extracted 1218 characters from diseases/ACIDOSIS.pdf
2025-07-25 11:20:47,386 - __main__ - INFO - Parsing medical document: ACIDOSIS.pdf
2025-07-25 11:20:47,386 - __main__ - INFO - Parsed document with 1059 symptoms for ACIDOSIS
2025-07-25 11:20:47,386 - __main__ - INFO - Found combined symptoms text for ACIDOSIS.pdf
2025-07-25 11:20:47,386 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:48,034 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:48,037 - __main__ - INFO - Inserting diagnosis record for ACIDOSIS
2025-07-25 11:20:48,059 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:48,083 - __main__ - INFO - Successfully inserted diagnosis record for ACIDOSIS
2025-07-25 11:20:48,084 - __main__ - INFO - Successfully processed combined symptoms from ACIDOSIS.pdf
2025-07-25 11:20:48,084 - __main__ - INFO - Processing file 15/34: Per Acute Mastitis.pdf
2025-07-25 11:20:48,084 - __main__ - INFO - Processing PDF: Per Acute Mastitis.pdf
2025-07-25 11:20:48,084 - __main__ - INFO - Extracting text from PDF: diseases/Per Acute Mastitis.pdf
2025-07-25 11:20:48,088 - __main__ - INFO - Successfully extracted 540 characters from diseases/Per Acute Mastitis.pdf
2025-07-25 11:20:48,088 - __main__ - INFO - Parsing medical document: Per Acute Mastitis.pdf
2025-07-25 11:20:48,088 - __main__ - INFO - Parsed document with 146 symptoms for Per Acute Mastitis
2025-07-25 11:20:48,088 - __main__ - INFO - Found combined symptoms text for Per Acute Mastitis.pdf
2025-07-25 11:20:48,088 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:49,194 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:49,198 - __main__ - INFO - Inserting diagnosis record for Per Acute Mastitis
2025-07-25 11:20:49,228 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:49,238 - __main__ - INFO - Successfully inserted diagnosis record for Per Acute Mastitis
2025-07-25 11:20:49,239 - __main__ - INFO - Successfully processed combined symptoms from Per Acute Mastitis.pdf
2025-07-25 11:20:49,239 - __main__ - INFO - Processing file 16/34: DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,239 - __main__ - INFO - Processing PDF: DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,239 - __main__ - INFO - Extracting text from PDF: diseases/DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,245 - __main__ - INFO - Successfully extracted 1741 characters from diseases/DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,245 - __main__ - INFO - Parsing medical document: DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,245 - __main__ - INFO - Parsed document with 1349 symptoms for DIARRHEA/ENTERITIS
2025-07-25 11:20:49,245 - __main__ - INFO - Found combined symptoms text for DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:49,246 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:50,429 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:50,434 - __main__ - INFO - Inserting diagnosis record for DIARRHEA/ENTERITIS
2025-07-25 11:20:50,460 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:50,469 - __main__ - INFO - Successfully inserted diagnosis record for DIARRHEA/ENTERITIS
2025-07-25 11:20:50,470 - __main__ - INFO - Successfully processed combined symptoms from DIARRHEA_ENTERITIS.pdf
2025-07-25 11:20:50,470 - __main__ - INFO - Processing file 17/34: Summer Mastitis.pdf
2025-07-25 11:20:50,470 - __main__ - INFO - Processing PDF: Summer Mastitis.pdf
2025-07-25 11:20:50,470 - __main__ - INFO - Extracting text from PDF: diseases/Summer Mastitis.pdf
2025-07-25 11:20:50,474 - __main__ - INFO - Successfully extracted 476 characters from diseases/Summer Mastitis.pdf
2025-07-25 11:20:50,474 - __main__ - INFO - Parsing medical document: Summer Mastitis.pdf
2025-07-25 11:20:50,474 - __main__ - INFO - Parsed document with 144 symptoms for Summer Mastitis
2025-07-25 11:20:50,474 - __main__ - INFO - Found combined symptoms text for Summer Mastitis.pdf
2025-07-25 11:20:50,474 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:51,685 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:51,688 - __main__ - INFO - Inserting diagnosis record for Summer Mastitis
2025-07-25 11:20:51,710 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:51,725 - __main__ - INFO - Successfully inserted diagnosis record for Summer Mastitis
2025-07-25 11:20:51,725 - __main__ - INFO - Successfully processed combined symptoms from Summer Mastitis.pdf
2025-07-25 11:20:51,725 - __main__ - INFO - Processing file 18/34: ANEMIA.pdf
2025-07-25 11:20:51,725 - __main__ - INFO - Processing PDF: ANEMIA.pdf
2025-07-25 11:20:51,725 - __main__ - INFO - Extracting text from PDF: diseases/ANEMIA.pdf
2025-07-25 11:20:51,734 - __main__ - INFO - Successfully extracted 1124 characters from diseases/ANEMIA.pdf
2025-07-25 11:20:51,734 - __main__ - INFO - Parsing medical document: ANEMIA.pdf
2025-07-25 11:20:51,735 - __main__ - INFO - Parsed document with 892 symptoms for ANEMIA
2025-07-25 11:20:51,735 - __main__ - INFO - Found combined symptoms text for ANEMIA.pdf
2025-07-25 11:20:51,735 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:52,871 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:52,874 - __main__ - INFO - Inserting diagnosis record for ANEMIA
2025-07-25 11:20:52,905 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:52,920 - __main__ - INFO - Successfully inserted diagnosis record for ANEMIA
2025-07-25 11:20:52,921 - __main__ - INFO - Successfully processed combined symptoms from ANEMIA.pdf
2025-07-25 11:20:52,921 - __main__ - INFO - Processing file 19/34: DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,921 - __main__ - INFO - Processing PDF: DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,921 - __main__ - INFO - Extracting text from PDF: diseases/DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,930 - __main__ - INFO - Successfully extracted 936 characters from diseases/DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,930 - __main__ - INFO - Parsing medical document: DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,931 - __main__ - INFO - Parsed document with 795 symptoms for DYSTOCIA COMPLICATION
2025-07-25 11:20:52,931 - __main__ - INFO - Found combined symptoms text for DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:52,931 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:54,737 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:54,739 - __main__ - INFO - Inserting diagnosis record for DYSTOCIA COMPLICATION
2025-07-25 11:20:54,756 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:54,762 - __main__ - INFO - Successfully inserted diagnosis record for DYSTOCIA COMPLICATION
2025-07-25 11:20:54,762 - __main__ - INFO - Successfully processed combined symptoms from DYSTOCIA COMPLICATION.pdf
2025-07-25 11:20:54,762 - __main__ - INFO - Processing file 20/34: Gangrenous Mastitis.pdf
2025-07-25 11:20:54,763 - __main__ - INFO - Processing PDF: Gangrenous Mastitis.pdf
2025-07-25 11:20:54,763 - __main__ - INFO - Extracting text from PDF: diseases/Gangrenous Mastitis.pdf
2025-07-25 11:20:54,765 - __main__ - INFO - Successfully extracted 531 characters from diseases/Gangrenous Mastitis.pdf
2025-07-25 11:20:54,765 - __main__ - INFO - Parsing medical document: Gangrenous Mastitis.pdf
2025-07-25 11:20:54,766 - __main__ - INFO - Parsed document with 193 symptoms for Gangrenous Mastitis
2025-07-25 11:20:54,766 - __main__ - INFO - Found combined symptoms text for Gangrenous Mastitis.pdf
2025-07-25 11:20:54,766 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:55,897 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:55,901 - __main__ - INFO - Inserting diagnosis record for Gangrenous Mastitis
2025-07-25 11:20:55,923 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:55,935 - __main__ - INFO - Successfully inserted diagnosis record for Gangrenous Mastitis
2025-07-25 11:20:55,935 - __main__ - INFO - Successfully processed combined symptoms from Gangrenous Mastitis.pdf
2025-07-25 11:20:55,935 - __main__ - INFO - Processing file 21/34: Mild Mastitis.pdf
2025-07-25 11:20:55,935 - __main__ - INFO - Processing PDF: Mild Mastitis.pdf
2025-07-25 11:20:55,935 - __main__ - INFO - Extracting text from PDF: diseases/Mild Mastitis.pdf
2025-07-25 11:20:55,940 - __main__ - INFO - Successfully extracted 500 characters from diseases/Mild Mastitis.pdf
2025-07-25 11:20:55,940 - __main__ - INFO - Parsing medical document: Mild Mastitis.pdf
2025-07-25 11:20:55,941 - __main__ - INFO - Parsed document with 243 symptoms for Mild Mastitis
2025-07-25 11:20:55,941 - __main__ - INFO - Found combined symptoms text for Mild Mastitis.pdf
2025-07-25 11:20:55,941 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:57,138 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:57,141 - __main__ - INFO - Inserting diagnosis record for Mild Mastitis
2025-07-25 11:20:57,158 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:57,162 - __main__ - INFO - Successfully inserted diagnosis record for Mild Mastitis
2025-07-25 11:20:57,162 - __main__ - INFO - Successfully processed combined symptoms from Mild Mastitis.pdf
2025-07-25 11:20:57,162 - __main__ - INFO - Processing file 22/34: EYE INFECTION.pdf
2025-07-25 11:20:57,162 - __main__ - INFO - Processing PDF: EYE INFECTION.pdf
2025-07-25 11:20:57,162 - __main__ - INFO - Extracting text from PDF: diseases/EYE INFECTION.pdf
2025-07-25 11:20:57,170 - __main__ - INFO - Successfully extracted 2131 characters from diseases/EYE INFECTION.pdf
2025-07-25 11:20:57,170 - __main__ - INFO - Parsing medical document: EYE INFECTION.pdf
2025-07-25 11:20:57,171 - __main__ - INFO - Parsed document with 1256 symptoms for EYE INFECTION
2025-07-25 11:20:57,171 - __main__ - INFO - Found combined symptoms text for EYE INFECTION.pdf
2025-07-25 11:20:57,171 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:20:58,978 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:20:58,982 - __main__ - INFO - Inserting diagnosis record for EYE INFECTION
2025-07-25 11:20:59,014 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:20:59,025 - __main__ - INFO - Successfully inserted diagnosis record for EYE INFECTION
2025-07-25 11:20:59,025 - __main__ - INFO - Successfully processed combined symptoms from EYE INFECTION.pdf
2025-07-25 11:20:59,025 - __main__ - INFO - Processing file 23/34: TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,025 - __main__ - INFO - Processing PDF: TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,026 - __main__ - INFO - Extracting text from PDF: diseases/TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,033 - __main__ - INFO - Successfully extracted 1275 characters from diseases/TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,033 - __main__ - INFO - Parsing medical document: TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,033 - __main__ - INFO - Parsed document with 1011 symptoms for TYMPANY/BLOAT
2025-07-25 11:20:59,033 - __main__ - INFO - Found combined symptoms text for TYMPANY_BLOAT.pdf
2025-07-25 11:20:59,033 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:00,351 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:00,353 - __main__ - INFO - Inserting diagnosis record for TYMPANY/BLOAT
2025-07-25 11:21:00,385 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:00,395 - __main__ - INFO - Successfully inserted diagnosis record for TYMPANY/BLOAT
2025-07-25 11:21:00,395 - __main__ - INFO - Successfully processed combined symptoms from TYMPANY_BLOAT.pdf
2025-07-25 11:21:00,395 - __main__ - INFO - Processing file 24/34: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,395 - __main__ - INFO - Processing PDF: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,395 - __main__ - INFO - Extracting text from PDF: diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,400 - __main__ - INFO - Successfully extracted 931 characters from diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,401 - __main__ - INFO - Parsing medical document: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,401 - __main__ - INFO - Parsed document with 751 symptoms for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS
2025-07-25 11:21:00,401 - __main__ - INFO - Found combined symptoms text for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:00,401 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:01,502 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:01,504 - __main__ - INFO - Inserting diagnosis record for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS
2025-07-25 11:21:01,527 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:01,542 - __main__ - INFO - Successfully inserted diagnosis record for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS
2025-07-25 11:21:01,542 - __main__ - INFO - Successfully processed combined symptoms from SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-07-25 11:21:01,543 - __main__ - INFO - Processing file 25/34: RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,543 - __main__ - INFO - Processing PDF: RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,543 - __main__ - INFO - Extracting text from PDF: diseases/RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,547 - __main__ - INFO - Successfully extracted 1497 characters from diseases/RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,548 - __main__ - INFO - Parsing medical document: RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,548 - __main__ - INFO - Parsed document with 1185 symptoms for RETENTION OF PLACENTA
2025-07-25 11:21:01,548 - __main__ - INFO - Found combined symptoms text for RETENTION OF PLACENTA.pdf
2025-07-25 11:21:01,548 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:02,967 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:02,969 - __main__ - INFO - Inserting diagnosis record for RETENTION OF PLACENTA
2025-07-25 11:21:02,990 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:03,004 - __main__ - INFO - Successfully inserted diagnosis record for RETENTION OF PLACENTA
2025-07-25 11:21:03,005 - __main__ - INFO - Successfully processed combined symptoms from RETENTION OF PLACENTA.pdf
2025-07-25 11:21:03,005 - __main__ - INFO - Processing file 26/34: STRESS.pdf
2025-07-25 11:21:03,005 - __main__ - INFO - Processing PDF: STRESS.pdf
2025-07-25 11:21:03,005 - __main__ - INFO - Extracting text from PDF: diseases/STRESS.pdf
2025-07-25 11:21:03,013 - __main__ - INFO - Successfully extracted 2342 characters from diseases/STRESS.pdf
2025-07-25 11:21:03,013 - __main__ - INFO - Parsing medical document: STRESS.pdf
2025-07-25 11:21:03,014 - __main__ - INFO - Parsed document with 1687 symptoms for STRESS
2025-07-25 11:21:03,014 - __main__ - INFO - Found combined symptoms text for STRESS.pdf
2025-07-25 11:21:03,014 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:04,189 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:04,191 - __main__ - INFO - Inserting diagnosis record for STRESS
2025-07-25 11:21:04,219 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:04,233 - __main__ - INFO - Successfully inserted diagnosis record for STRESS
2025-07-25 11:21:04,233 - __main__ - INFO - Successfully processed combined symptoms from STRESS.pdf
2025-07-25 11:21:04,233 - __main__ - INFO - Processing file 27/34: EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,234 - __main__ - INFO - Processing PDF: EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,234 - __main__ - INFO - Extracting text from PDF: diseases/EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,241 - __main__ - INFO - Successfully extracted 891 characters from diseases/EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,241 - __main__ - INFO - Parsing medical document: EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,241 - __main__ - INFO - Parsed document with 732 symptoms for EPHEMERAL FEVER
2025-07-25 11:21:04,241 - __main__ - INFO - Found combined symptoms text for EPHEMERAL FEVER.pdf
2025-07-25 11:21:04,241 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:05,396 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:05,400 - __main__ - INFO - Inserting diagnosis record for EPHEMERAL FEVER
2025-07-25 11:21:05,425 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:05,437 - __main__ - INFO - Successfully inserted diagnosis record for EPHEMERAL FEVER
2025-07-25 11:21:05,437 - __main__ - INFO - Successfully processed combined symptoms from EPHEMERAL FEVER.pdf
2025-07-25 11:21:05,437 - __main__ - INFO - Processing file 28/34: Acute Mastitis.pdf
2025-07-25 11:21:05,437 - __main__ - INFO - Processing PDF: Acute Mastitis.pdf
2025-07-25 11:21:05,437 - __main__ - INFO - Extracting text from PDF: diseases/Acute Mastitis.pdf
2025-07-25 11:21:05,441 - __main__ - INFO - Successfully extracted 525 characters from diseases/Acute Mastitis.pdf
2025-07-25 11:21:05,441 - __main__ - INFO - Parsing medical document: Acute Mastitis.pdf
2025-07-25 11:21:05,442 - __main__ - INFO - Parsed document with 129 symptoms for Acute Mastitis
2025-07-25 11:21:05,442 - __main__ - INFO - Found combined symptoms text for Acute Mastitis.pdf
2025-07-25 11:21:05,442 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:06,592 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:06,595 - __main__ - INFO - Inserting diagnosis record for Acute Mastitis
2025-07-25 11:21:06,618 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:06,630 - __main__ - INFO - Successfully inserted diagnosis record for Acute Mastitis
2025-07-25 11:21:06,631 - __main__ - INFO - Successfully processed combined symptoms from Acute Mastitis.pdf
2025-07-25 11:21:06,631 - __main__ - INFO - Processing file 29/34: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,632 - __main__ - INFO - Processing PDF: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,632 - __main__ - INFO - Extracting text from PDF: diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,641 - __main__ - INFO - Successfully extracted 1706 characters from diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,642 - __main__ - INFO - Parsing medical document: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,642 - __main__ - INFO - Parsed document with 1208 symptoms for ERGOT POISONING (DEGNALA DISEASE)
2025-07-25 11:21:06,642 - __main__ - INFO - Found combined symptoms text for ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:06,642 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:07,211 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:07,214 - __main__ - INFO - Inserting diagnosis record for ERGOT POISONING (DEGNALA DISEASE)
2025-07-25 11:21:07,237 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:07,249 - __main__ - INFO - Successfully inserted diagnosis record for ERGOT POISONING (DEGNALA DISEASE)
2025-07-25 11:21:07,249 - __main__ - INFO - Successfully processed combined symptoms from ERGOT POISONING (DEGNALA DISEASE).pdf
2025-07-25 11:21:07,249 - __main__ - INFO - Processing file 30/34: ANAPLASMOSIS.pdf
2025-07-25 11:21:07,249 - __main__ - INFO - Processing PDF: ANAPLASMOSIS.pdf
2025-07-25 11:21:07,250 - __main__ - INFO - Extracting text from PDF: diseases/ANAPLASMOSIS.pdf
2025-07-25 11:21:07,255 - __main__ - INFO - Successfully extracted 511 characters from diseases/ANAPLASMOSIS.pdf
2025-07-25 11:21:07,255 - __main__ - INFO - Parsing medical document: ANAPLASMOSIS.pdf
2025-07-25 11:21:07,256 - __main__ - INFO - Parsed document with 411 symptoms for ANAPLASMOSIS
2025-07-25 11:21:07,256 - __main__ - INFO - Found combined symptoms text for ANAPLASMOSIS.pdf
2025-07-25 11:21:07,256 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:08,387 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:08,389 - __main__ - INFO - Inserting diagnosis record for ANAPLASMOSIS
2025-07-25 11:21:08,405 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:08,413 - __main__ - INFO - Successfully inserted diagnosis record for ANAPLASMOSIS
2025-07-25 11:21:08,413 - __main__ - INFO - Successfully processed combined symptoms from ANAPLASMOSIS.pdf
2025-07-25 11:21:08,414 - __main__ - INFO - Processing file 31/34: Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,414 - __main__ - INFO - Processing PDF: Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,414 - __main__ - INFO - Extracting text from PDF: diseases/Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,417 - __main__ - INFO - Successfully extracted 494 characters from diseases/Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,417 - __main__ - INFO - Parsing medical document: Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,417 - __main__ - INFO - Parsed document with 140 symptoms for Sub Acute Mastitis
2025-07-25 11:21:08,417 - __main__ - INFO - Found combined symptoms text for Sub-Acute Mastitis.pdf
2025-07-25 11:21:08,417 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:09,601 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:09,603 - __main__ - INFO - Inserting diagnosis record for Sub Acute Mastitis
2025-07-25 11:21:09,651 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:09,666 - __main__ - INFO - Successfully inserted diagnosis record for Sub Acute Mastitis
2025-07-25 11:21:09,666 - __main__ - INFO - Successfully processed combined symptoms from Sub-Acute Mastitis.pdf
2025-07-25 11:21:09,666 - __main__ - INFO - Processing file 32/34: SKIN INFECTION.pdf
2025-07-25 11:21:09,667 - __main__ - INFO - Processing PDF: SKIN INFECTION.pdf
2025-07-25 11:21:09,667 - __main__ - INFO - Extracting text from PDF: diseases/SKIN INFECTION.pdf
2025-07-25 11:21:09,672 - __main__ - INFO - Successfully extracted 787 characters from diseases/SKIN INFECTION.pdf
2025-07-25 11:21:09,672 - __main__ - INFO - Parsing medical document: SKIN INFECTION.pdf
2025-07-25 11:21:09,672 - __main__ - INFO - Parsed document with 569 symptoms for SKIN INFECTION
2025-07-25 11:21:09,672 - __main__ - INFO - Found combined symptoms text for SKIN INFECTION.pdf
2025-07-25 11:21:09,672 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:10,407 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:10,409 - __main__ - INFO - Inserting diagnosis record for SKIN INFECTION
2025-07-25 11:21:10,442 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:10,449 - __main__ - INFO - Successfully inserted diagnosis record for SKIN INFECTION
2025-07-25 11:21:10,450 - __main__ - INFO - Successfully processed combined symptoms from SKIN INFECTION.pdf
2025-07-25 11:21:10,450 - __main__ - INFO - Processing file 33/34: COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,450 - __main__ - INFO - Processing PDF: COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,451 - __main__ - INFO - Extracting text from PDF: diseases/COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,457 - __main__ - INFO - Successfully extracted 1656 characters from diseases/COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,457 - __main__ - INFO - Parsing medical document: COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,457 - __main__ - INFO - Parsed document with 1212 symptoms for COLIC PAIN IN ADULT
2025-07-25 11:21:10,458 - __main__ - INFO - Found combined symptoms text for COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:10,458 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:11,913 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:11,914 - __main__ - INFO - Inserting diagnosis record for COLIC PAIN IN ADULT
2025-07-25 11:21:11,944 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:11,961 - __main__ - INFO - Successfully inserted diagnosis record for COLIC PAIN IN ADULT
2025-07-25 11:21:11,961 - __main__ - INFO - Successfully processed combined symptoms from COLIC PAIN IN ADULT.pdf
2025-07-25 11:21:11,961 - __main__ - INFO - Processing file 34/34: IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,961 - __main__ - INFO - Processing PDF: IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,961 - __main__ - INFO - Extracting text from PDF: diseases/IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,966 - __main__ - INFO - Successfully extracted 838 characters from diseases/IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,966 - __main__ - INFO - Parsing medical document: IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,967 - __main__ - INFO - Parsed document with 663 symptoms for IMPACTION OF RUMEN
2025-07-25 11:21:11,967 - __main__ - INFO - Found combined symptoms text for IMPACTION OF RUMEN.pdf
2025-07-25 11:21:11,967 - __main__ - INFO - Generating embeddings for 1 texts using OpenAI API
2025-07-25 11:21:13,125 - __main__ - INFO - Generated 1 embeddings successfully
2025-07-25 11:21:13,127 - __main__ - INFO - Inserting diagnosis record for IMPACTION OF RUMEN
2025-07-25 11:21:13,147 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:13,159 - __main__ - INFO - Successfully inserted diagnosis record for IMPACTION OF RUMEN
2025-07-25 11:21:13,159 - __main__ - INFO - Successfully processed combined symptoms from IMPACTION OF RUMEN.pdf
2025-07-25 11:21:13,184 - __main__ - INFO - Database connection established on attempt 1
2025-07-25 11:21:13,200 - __main__ - INFO - Processing complete! Total records in database: 34
