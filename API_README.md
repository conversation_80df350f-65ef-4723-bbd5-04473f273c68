# Medical Diagnosis Chat API

A REST API for interactive medical diagnosis using vector search and AI-powered follow-up questions.

## Overview

This API provides endpoints for:

- Starting diagnosis chat sessions with initial symptoms
- Answering follow-up questions to narrow down diagnoses
- Getting session status and diagnosis results
- Health monitoring

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_api.txt
```

### 2. Set Environment Variables

Create a `.env` file with:

```bash
# Database Configuration
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# Or use DATABASE_URL instead
# DATABASE_URL=postgresql://user:pass@host:port/dbname

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here  # Optional, for alternative LLM

# API Configuration
API_PORT=5000
FLASK_DEBUG=false
```

### 3. Start the API Server

```bash
python diagnosis_api.py
```

The API will be available at `http://localhost:5000`

### 4. Test the API

```bash
# Using the provided client
python diagnosis_api_client.py

# Or using curl
curl -X GET http://localhost:5000/api/health
```

## API Endpoints

### Health Check

**GET** `/api/health`

Check API health and database connectivity.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "database_diagnoses": 1500,
  "active_sessions": 3
}
```

### Start Chat Session

**POST** `/api/chat/start`

Start a new diagnosis chat session with initial symptoms.

**Request Body:**

```json
{
  "symptoms": "The cow has reduced milk production and seems lethargic"
}
```

**Response:**

```json
{
  "session_id": "uuid-string",
  "message": "Chat session started successfully",
  "type": "follow_up_question",
  "question": "Is the udder swollen or warm to touch?",
  "question_number": 1,
  "max_questions": 5,
  "top_diagnoses": [
    {
      "name": "Acute Mastitis",
      "confidence_score": 0.75,
      "symptoms": "Reduced milk production, lethargy...",
      "description": "Inflammation of mammary tissue..."
    }
  ],
  "is_completed": false
}
```

### Answer Question

**POST** `/api/chat/{session_id}/answer`

Answer a follow-up question in an existing session.

**Request Body:**

```json
{
  "answer": "Yes, the udder feels warm and swollen"
}
```

**Response:**

```json
{
  "type": "follow_up_question",
  "question": "Is there any discharge from the udder?",
  "question_number": 2,
  "max_questions": 5,
  "top_diagnoses": [...],
  "is_completed": false
}
```

**Final Diagnosis Response:**

```json
{
  "type": "final_diagnosis",
  "diagnosis": {
    "name": "Acute Mastitis",
    "confidence_score": 0.85,
    "description": "Acute inflammation of mammary tissue...",
    "symptoms": "Swollen udder, reduced milk production...",
    "treatment": "Antibiotic therapy, anti-inflammatory...",
    "notes": "Monitor closely for complications..."
  },
  "top_diagnoses": [...],
  "is_completed": true,
  "confidence_threshold_met": true
}
```

### Get Session Status

**GET** `/api/chat/{session_id}/status`

Get current status of a chat session.

**Response:**

```json
{
  "session_id": "uuid-string",
  "question_count": 2,
  "max_questions": 5,
  "is_completed": false,
  "symptoms_history": [
    "The cow has reduced milk production and seems lethargic",
    "Yes, the udder feels warm and swollen"
  ],
  "previous_questions": [
    "Is the udder swollen or warm to touch?",
    "Is there any discharge from the udder?"
  ],
  "conversation_history": [
    {
      "step": 1,
      "type": "initial_symptoms",
      "content": "The cow has reduced milk production and seems lethargic"
    },
    {
      "step": 2,
      "type": "question",
      "content": "Is the udder swollen or warm to touch?",
      "question_number": 1
    },
    {
      "step": 3,
      "type": "answer",
      "content": "Yes, the udder feels warm and swollen",
      "question_number": 1
    }
  ],
  "current_diagnoses": [...]
}
```

## Conversation History Format

The API tracks the complete conversation history in a structured JSON format. Each step in the conversation is recorded with the following structure:

```json
{
  "step": 1, // Sequential step number
  "type": "initial_symptoms", // Type of step: "initial_symptoms", "question", "answer"
  "content": "...", // The actual content (symptoms, question, or answer)
  "question_number": 1 // Question number (only for questions and answers)
}
```

### Step Types:

- **`initial_symptoms`**: The first symptoms provided by the user
- **`question`**: Follow-up questions asked by the AI
- **`answer`**: User responses to follow-up questions

This structured format allows the AI to:

- Avoid repeating previous questions
- Understand the context of previous answers
- Make more informed decisions about next questions
- Provide better diagnosis recommendations

## Response Types

### Follow-up Question

- `type`: "follow_up_question"
- Contains next question to ask
- Includes current top diagnoses
- Session continues

### Final Diagnosis

- `type`: "final_diagnosis"
- Contains final diagnosis recommendation
- Session is completed
- Includes confidence score and treatment info

### No Matches

- `type`: "no_matches"
- No suitable diagnoses found
- Recommends consulting medical professional
- Session is completed

### Error

- Contains `error` field with error message
- Session may continue depending on error type

## Error Handling

The API returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad Request (missing/invalid parameters)
- `404`: Session not found or expired
- `500`: Internal server error

Error responses include an `error` field:

```json
{
  "error": "Session not found or expired"
}
```

## Session Management

- Sessions are stored in memory (use Redis for production)
- Sessions expire after 1 hour of inactivity
- Each session maintains its own state and history
- Session IDs are UUIDs for security

## Usage Examples

### Python Client

```python
from diagnosis_api_client import DiagnosisAPIClient

client = DiagnosisAPIClient("http://localhost:5000")

# Start chat
result = client.start_chat("Cow has fever and reduced appetite")

# Answer questions
while result.get('type') == 'follow_up_question':
    question = result['question']
    answer = input(f"Q: {question}\nA: ")
    result = client.answer_question(answer)

# Get final diagnosis
if result.get('type') == 'final_diagnosis':
    diagnosis = result['diagnosis']
    print(f"Diagnosis: {diagnosis['name']}")
    print(f"Confidence: {diagnosis['confidence_score'] * 100:.1f}%")
```

### cURL Examples

```bash
# Health check
curl -X GET http://localhost:5000/api/health

# Start chat
curl -X POST http://localhost:5000/api/chat/start \
  -H "Content-Type: application/json" \
  -d '{"symptoms": "Cow has fever and reduced appetite"}'

# Answer question
curl -X POST http://localhost:5000/api/chat/SESSION_ID/answer \
  -H "Content-Type: application/json" \
  -d '{"answer": "Yes, the cow is not eating"}'

# Get status
curl -X GET http://localhost:5000/api/chat/SESSION_ID/status
```

## Production Considerations

1. **Session Storage**: Use Redis or database instead of in-memory storage
2. **Authentication**: Add API key or JWT authentication
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Logging**: Enhanced logging for monitoring and debugging
5. **HTTPS**: Use HTTPS in production
6. **Database**: Use connection pooling for better performance
7. **Caching**: Cache embeddings and frequent queries

## Dependencies

- Flask: Web framework
- Flask-CORS: Cross-origin resource sharing
- psycopg2: PostgreSQL adapter
- requests: HTTP library
- python-dotenv: Environment variable management
- openai: OpenAI API client

## License

This project is part of the medical diagnosis system.
