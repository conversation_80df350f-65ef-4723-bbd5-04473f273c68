# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Documentation
README.md
API_README.md
*.md

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Test files
test_*
*_test.py
tests/

# Temporary files
tmp/
temp/
.tmp/

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Environment files (will be copied explicitly)
# .env files are copied explicitly in Dockerfile
