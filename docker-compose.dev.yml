# Development Docker Compose configuration
# Use this for local development with hot reloading

version: '3.8'

services:
  # PostgreSQL Database (same as production)
  postgres:
    image: postgres:15-alpine
    container_name: diagnosis_postgres_dev
    environment:
      POSTGRES_DB: ${DB_NAME:-kforce}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-kforce}
    ports:
      - "${DB_PORT:-5444}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - diagnosis_network
    restart: unless-stopped

  # Redis for session storage
  redis:
    image: redis:7-alpine
    container_name: diagnosis_redis_dev
    ports:
      - "6379:6379"
    networks:
      - diagnosis_network
    restart: unless-stopped

  # Diagnosis API with development settings
  diagnosis_api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: diagnosis_api_dev
    environment:
      # Database configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-kforce}
      DB_USER: ${DB_USER:-postgres}
      DB_PASS: ${DB_PASS:-kforce}
      
      # Development settings
      API_PORT: 5000
      FLASK_DEBUG: true
      FLASK_ENV: development
      
      # API Keys
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      XAI_API_KEY: ${XAI_API_KEY}
      
      # Redis configuration
      REDIS_URL: redis://redis:6379/0
    ports:
      - "${API_PORT:-5000}:5000"
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - ./logs:/app/logs
    networks:
      - diagnosis_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: ["python", "-u", "diagnosis_api.py"]

volumes:
  postgres_dev_data:
    driver: local

networks:
  diagnosis_network:
    driver: bridge
