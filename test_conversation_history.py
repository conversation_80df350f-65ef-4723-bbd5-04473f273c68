#!/usr/bin/env python3
"""
Test script to verify conversation history JSON formatting
"""

import json
from typing import List, Dict

def test_conversation_history_format():
    """Test the conversation history JSON format"""
    
    # Sample conversation history
    conversation_history = [
        {
            "step": 1,
            "type": "initial_symptoms",
            "content": "The cow has reduced milk production and seems lethargic"
        },
        {
            "step": 2,
            "type": "question",
            "content": "Is the udder swollen or warm to touch?",
            "question_number": 1
        },
        {
            "step": 3,
            "type": "answer",
            "content": "Yes, the udder feels warm and swollen",
            "question_number": 1
        },
        {
            "step": 4,
            "type": "question",
            "content": "Is there any discharge from the udder?",
            "question_number": 2
        },
        {
            "step": 5,
            "type": "answer",
            "content": "Yes, there's some yellowish discharge",
            "question_number": 2
        }
    ]
    
    # Test JSON serialization
    try:
        conversation_json = json.dumps(conversation_history, indent=2)
        print("✅ JSON serialization successful!")
        print("\nFormatted conversation history:")
        print("=" * 50)
        print(conversation_json)
        
        # Test deserialization
        parsed_history = json.loads(conversation_json)
        print("\n✅ JSON deserialization successful!")
        
        # Verify structure
        print(f"\nConversation has {len(parsed_history)} steps:")
        for step in parsed_history:
            step_num = step.get('step', 'Unknown')
            step_type = step.get('type', 'Unknown')
            content_preview = step.get('content', '')[:50] + "..." if len(step.get('content', '')) > 50 else step.get('content', '')
            print(f"  Step {step_num}: {step_type} - {content_preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON formatting failed: {e}")
        return False

def extract_symptoms_summary(conversation_history):
    """Extract a clean summary of current symptoms from conversation history"""
    if not conversation_history:
        return "No symptoms provided"

    # Get initial symptoms
    initial_symptoms = ""
    additional_info = []

    for step in conversation_history:
        if step.get('type') == 'initial_symptoms':
            initial_symptoms = step.get('content', '')
        elif step.get('type') == 'answer':
            # Only include relevant symptom information from answers
            answer = step.get('content', '').strip()
            if answer and not answer.lower().startswith('no'):
                additional_info.append(answer)

    # Combine initial symptoms with relevant additional information
    if additional_info:
        return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
    else:
        return initial_symptoms

def test_prompt_generation():
    """Test how the conversation history would appear in the prompt"""

    conversation_history = [
        {
            "step": 1,
            "type": "initial_symptoms",
            "content": "The cow has reduced milk production and seems lethargic"
        },
        {
            "step": 2,
            "type": "question",
            "content": "Has there been any noticeable change in the appearance of the milk, such as discoloration or the presence of flakes?",
            "question_number": 1
        },
        {
            "step": 3,
            "type": "answer",
            "content": "yes, there is discolloration of milk",
            "question_number": 1
        }
    ]

    # Current symptoms (clean summary from conversation history)
    symptoms_summary = extract_symptoms_summary(conversation_history)
    
    # Format conversation history as JSON
    conversation_json = json.dumps(conversation_history, indent=2)
    
    # Sample top diagnoses (matching your example)
    top_diagnoses = [
        {
            "name": "Sub Acute Mastitis",
            "confidence_score": 0.61,
            "symptoms": "Milk discoloration visible, CMT positive (+++), Clotted milk and maximum flakes, Swollen udder (not hot or painful), Normal feeding and no fever..."
        },
        {
            "name": "Chronic Fibrosis Mastitis",
            "confidence_score": 0.58,
            "symptoms": "Atrophied or fibrotic udder, CMT positive (++++), Discolored, clotted, or flaky milk, Normal feeding..."
        },
        {
            "name": "Mild Mastitis",
            "confidence_score": 0.56,
            "symptoms": "No visible discoloration of milk, CMT result is ++, No fever; normal feeding, A few milk flakes and slight warmth in the udder..."
        }
    ]
    
    diagnosis_context = "\n".join([
        f"- {d['name']} (confidence: {d['confidence_score']:.2f}): {d['symptoms'][:200]}..."
        for d in top_diagnoses[:3]
    ])
    
    # Generate the prompt as it would appear
    prompt = f"""You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: {symptoms_summary}

Conversation history (JSON format):
{conversation_json}

Top potential diagnoses:
{diagnosis_context}

IMPORTANT:
- Ask a specific, targeted question that would help differentiate between these diagnoses
- Do NOT repeat or rephrase any previous questions from the conversation history
- Do NOT ask about excluded diagnoses or their symptoms
- If the diagnoses are too similar or no good differentiating question exists, respond with "NO_QUESTION"
- Keep it concise and medical professional
- Only ask ONE question"""
    
    print("\n" + "=" * 60)
    print("SAMPLE PROMPT WITH CONVERSATION HISTORY")
    print("=" * 60)
    print(prompt)
    print("=" * 60)

def main():
    """Main test function"""
    print("🧪 Testing Conversation History JSON Formatting")
    print("=" * 50)
    
    # Test basic JSON formatting
    if test_conversation_history_format():
        print("\n✅ All JSON formatting tests passed!")
    else:
        print("\n❌ JSON formatting tests failed!")
        return
    
    # Test prompt generation
    test_prompt_generation()
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
